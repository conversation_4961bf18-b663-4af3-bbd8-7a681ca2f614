# Database Configuration

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=xKylnVNyXSRZ3+3BFDWHaGApfOoT9Uq1yhqhGDPx
AWS_REGION=ap-southeast-2
S3_BUCKET_NAME=growthhive-stage
S3_BASE_URL=https://growthhive-stage.s3.ap-southeast-2.amazonaws.com

# Development Settings
ENVIRONMENT=staging
DEBUG=true

# File Upload Settings
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/growthhive.log

# Database URL - Local Development
DATABASE_URL=postgresql+asyncpg://growthhive_stage:<EMAIL>:5432/growthhive_stage


# Zoho CRM Integration Settings - Australian Domain
ZOHO_CLIENT_ID=1000.PRYTZ0VSO7PV6KL8ZU0TLLMOF8BU5A
ZOHO_CLIENT_SECRET=d2891e7f56126961192d2c98856add5e3f6a7082be
ZOHO_REFRESH_TOKEN=**********************************************************************
ZOHO_BASE_URL=https://www.zohoapis.com.au/crm/v2
ZOHO_AUTH_URL=https://accounts.zoho.com.au/oauth/v2/token
ZOHO_SYNC_INTERVAL_MINUTES=15
