"""
General Message API Endpoints
REST API endpoints for general message management
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, Path
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database.connection import get_db
from app.services.general_message_service import GeneralMessageService
from app.schemas.general_message import (
    GeneralMessageUpdateRequest,
    GeneralMessageResponse
)
from app.core.app_rules import ApiResponse
from app.core.security.enhanced_auth_middleware import get_current_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get(
    "/type/{message_type}",
    response_model=ApiResponse,
    summary="Fetch General Message by Type",
    description="Fetch a general message by message_type",
    responses={
        200: {
            "description": "General message retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "General Message Retrieved",
                        "description": "Successfully retrieved general message for type 'info'",
                        "details": {
                            "id": "550e8400-e29b-41d4-a716-446655440000",
                            "message": "Welcome to the system!",
                            "message_type": "info"
                        }
                    }
                }
            }
        },
        404: {"description": "Message not found for the specified type"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def fetch_general_msg_by_type(
    message_type: str = Path(..., description="Message type to fetch", example="info"),
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Fetch a general message by message_type.
    
    Available message types:
    - info
    - warning
    - error
    - success
    - notification
    - alert
    """
    logger.info("API: fetch_general_msg_by_type called", extra={
        "message_key": "FETCH_GENERAL_MSG_REQUEST",
        "message_type": message_type
    })
    
    service = GeneralMessageService(db)
    return await service.fetch_general_msg_by_type(message_type)


@router.put(
    "/type/{message_type}",
    response_model=ApiResponse,
    summary="Update General Message by Type",
    description="Update a general message by message_type",
    responses={
        200: {
            "description": "General message updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "General Message Updated",
                        "description": "Successfully updated general message for type 'info'",
                        "details": {
                            "id": "550e8400-e29b-41d4-a716-446655440000",
                            "message": "Updated welcome message!",
                            "message_type": "info"
                        }
                    }
                }
            }
        },
        404: {"description": "Message not found for the specified type"},
        401: {"description": "Unauthorized"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal server error"}
    }
)
async def update_general_message_by_type(
    message_data: GeneralMessageUpdateRequest,
    message_type: str = Path(..., description="Message type to update", example="info"),
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Update a general message by message_type.
    
    - **message**: Updated message content
    
    Available message types:
    - info
    - warning
    - error
    - success
    - notification
    - alert
    """
    logger.info("API: update_general_message_by_type called", extra={
        "message_key": "UPDATE_GENERAL_MSG_REQUEST",
        "message_type": message_type
    })
    
    service = GeneralMessageService(db)
    return await service.update_general_message_by_type(message_type, message_data)
