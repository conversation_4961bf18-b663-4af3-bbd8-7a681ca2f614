"""
Lead management endpoints with full Swagger documentation
"""

from typing import Optional, Annotated, Dict, Any
from datetime import datetime
from fastapi import APIRouter, Depends, Query, Path, Header, HTTPException, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.lead import (
    LeadCreateRequest,
    LeadUpdateRequest,
    LeadResponse,
    LeadListResponse,
    LeadSuccessResponse,
    LeadListSuccessResponse,
    LeadDeleteSuccessResponse,
    LeadStatus,
    BulkUploadSuccessResponse,
    CommunicationHistorySuccessResponse
)
from app.schemas.base_response import (
    COMMON_RESPONSES,
    ResponseMessage,
    PaginationInfo
)
from app.core.database.connection import get_db
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.core.factory import get_lead_service
from app.services.lead_service import LeadService
from app.core.logging import logger

router = APIRouter()


@router.get(
    "/",
    response_model=LeadListSuccessResponse,
    summary="List Leads",
    description="Retrieve a paginated list of leads with optional filtering, sorting, and search",
    responses={
        200: {
            "description": "Leads retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Leads Retrieved",
                            "description": "Leads retrieved successfully"
                        },
                        "data": {
                            "items": [
                                {
                                    "id": "550e8400-e29b-41d4-a716-446655440000",
                                    "zoho_lead_id": "ZOHO123",
                                    "full_name": "John Doe",
                                    "phone": "+1234567890",
                                    "mobile": "+0987654321",
                                    "email": "<EMAIL>",
                                    "location": "New York",
                                    "lead_source": "Website",
                                    "franchise_preference": "Food",
                                    "budget_preference": 75000.00,
                                    "qualification_status": "qualified",
                                    "created_at": "2024-01-01T00:00:00Z",
                                    "updated_at": "2024-01-01T00:00:00Z"
                                }
                            ],
                            "total_count": 1,
                            "pagination": {
                                "current_page": 1,
                                "total_pages": 1,
                                "items_per_page": 20,
                                "total_items": 1
                            }
                        }
                    }
                }
            }
        },
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    },
    dependencies=[Depends(get_current_user)]
)
async def list_leads(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    status: Optional[str] = Query(None, description="Filter by qualification status"),
    search: Optional[str] = Query(None, description="Search leads by name, email, or contact number"),
    lead_source: Optional[str] = Query(None, description="Filter by lead source"),
    created_from: Optional[datetime] = Query(None, description="Filter leads created from this date"),
    created_to: Optional[datetime] = Query(None, description="Filter leads created until this date"),
    sort: Optional[str] = Query(None, description="Sort order: name_asc, name_desc, created_asc, created_desc"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    lead_service: LeadService = Depends(get_lead_service)
):
    """List leads with pagination and filtering"""
    try:
        leads, total_count = await lead_service.get_leads(
            skip=skip,
            limit=limit,
            status=status,
            search=search,
            lead_source=lead_source,
            created_from=created_from,
            created_to=created_to,
            sort=sort,
            user_role=current_user.get("role"),
            user_id=current_user.get("user_id")
        )
        
        # Calculate pagination info
        page = (skip // limit) + 1
        pages = (total_count + limit - 1) // limit
        
        return LeadListSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Leads Retrieved",
                description="Leads retrieved successfully"
            ),
            data=LeadListResponse(
                items=leads,
                total_count=total_count,
                pagination=PaginationInfo(
                    current_page=page,
                    total_pages=pages,
                    items_per_page=limit,
                    total_items=total_count
                )
            )
        )
        
    except Exception as e:
        logger.error(f"Error in list_leads: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve leads"
        )


@router.get(
    "/{lead_id}",
    response_model=LeadSuccessResponse,
    summary="Get Lead by ID",
    description="Retrieve a specific lead by its unique identifier",
    responses={
        200: {
            "description": "Lead retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Lead Retrieved",
                            "description": "Lead retrieved successfully"
                        },
                        "data": {
                            "id": "550e8400-e29b-41d4-a716-446655440000",
                            "zoho_lead_id": "ZOHO123",
                            "full_name": "John Doe",
                            "contact_number": "+1234567890",
                            "email": "<EMAIL>",
                            "location": "New York",
                            "lead_source": "Website",
                            "franchise_preference": "Food",
                            "budget_preference": 75000.00,
                            "qualification_status": "qualified",
                            "created_at": "2024-01-01T00:00:00Z",
                            "updated_at": "2024-01-01T00:00:00Z"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_lead(
    lead_id: Annotated[str, Path(description="Unique lead identifier", example="550e8400-e29b-41d4-a716-446655440000")],
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
    lead_service: LeadService = Depends(get_lead_service)
):
    """
    Retrieve a specific lead by its unique identifier.
    
    This endpoint returns detailed information about a lead including
    all its properties and metadata.
    """
    try:
        lead = await lead_service.get_lead_by_id(
            lead_id,
            user_role=current_user.get("role"),
            user_id=current_user.get("user_id")
        )
        
        if not lead:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Lead Not Found",
                message_description=f"Lead with ID {lead_id} not found",
                status_code=404
            )

        response_data = await lead_service._lead_to_response(lead)

        return LeadSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Lead Retrieved",
                description="Lead retrieved successfully"
            ),
            data=response_data
        )
        
    except HTTPException:
        # Re-raise HTTPExceptions from service layer (they have proper error messages)
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_lead API: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while retrieving the lead"
        )


@router.patch(
    "/{lead_id}/activate",
    response_model=LeadSuccessResponse,
    summary="Activate Lead",
    description="Activate a lead by setting is_active=True",
    responses={
        200: {
            "description": "Lead activated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Lead Activated",
                            "description": "Lead activated successfully"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def activate_lead(
    lead_id: Annotated[str, Path(description="Unique lead identifier")],
    current_user: Dict[str, Any] = Depends(get_current_user),
    lead_service: LeadService = Depends(get_lead_service)
):
    """
    Activate a lead by setting is_active=True.

    This endpoint requires authentication and will set the lead's
    is_active status to True, making it available for operations.
    """
    try:
        lead = await lead_service.activate_lead(lead_id)

        if not lead:
            raise HTTPException(
                status_code=404,
                detail=f"Lead with ID {lead_id} not found"
            )

        return LeadSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Lead Activated",
                description="Lead activated successfully"
            ),
            data=lead
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error activating lead {lead_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to activate lead"
        )


@router.patch(
    "/{lead_id}/deactivate",
    response_model=LeadSuccessResponse,
    summary="Deactivate Lead",
    description="Deactivate a lead by setting is_active=False",
    responses={
        200: {
            "description": "Lead deactivated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Lead Deactivated",
                            "description": "Lead deactivated successfully"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def deactivate_lead(
    lead_id: Annotated[str, Path(description="Unique lead identifier")],
    current_user: Dict[str, Any] = Depends(get_current_user),
    lead_service: LeadService = Depends(get_lead_service)
):
    """
    Deactivate a lead by setting is_active=False.

    This endpoint requires authentication and will set the lead's
    is_active status to False, making it unavailable for operations.
    """
    try:
        lead = await lead_service.deactivate_lead(lead_id)

        if not lead:
            raise HTTPException(
                status_code=404,
                detail=f"Lead with ID {lead_id} not found"
            )

        return LeadSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Lead Deactivated",
                description="Lead deactivated successfully"
            ),
            data=lead
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deactivating lead {lead_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to deactivate lead"
        )


@router.post(
    "/",
    response_model=LeadSuccessResponse,
    summary="Create New Lead",
    description="Create a new lead with comprehensive validation and authentication",
    responses={
        201: {
            "description": "Lead created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Lead Created",
                            "description": "Lead created successfully"
                        },
                        "data": {
                            "id": "550e8400-e29b-41d4-a716-446655440000",
                            "zoho_lead_id": "ZOHO123",
                            "full_name": "John Doe",
                            "contact_number": "+1234567890",
                            "email": "<EMAIL>",
                            "location": "New York",
                            "lead_source": "Website",
                            "franchise_preference": "Food",
                            "budget_preference": 75000.00,
                            "qualification_status": "new",
                            "created_at": "2024-01-01T00:00:00Z",
                            "updated_at": "2024-01-01T00:00:00Z"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    status_code=201,
    dependencies=[Depends(get_current_user)]
)
async def create_lead(
    lead_data: LeadCreateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    lead_service: LeadService = Depends(get_lead_service)
):
    """
    Create a new lead with comprehensive validation.
    
    This endpoint creates a new lead with all required fields and optional
    metadata. The lead will be assigned a unique identifier and timestamp.
    """
    try:
        lead = await lead_service.create_lead(
            lead_data, 
            created_by=current_user.get("user_id")
        )
        
        response_data = await lead_service._lead_to_response(lead)

        return LeadSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Lead Created",
                description="Lead created successfully"
            ),
            data=response_data
        )
        
    except HTTPException:
        # Re-raise HTTPExceptions from service layer (they have proper error messages)
        raise
    except Exception as e:
        logger.error(f"Unexpected error in create_lead API: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while creating the lead"
        )


@router.put(
    "/{lead_id}",
    response_model=LeadSuccessResponse,
    summary="Update Lead",
    description="Update an existing lead with validation and authentication",
    responses={
        200: {
            "description": "Lead updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Lead Updated",
                            "description": "Lead updated successfully"
                        },
                        "data": {
                            "id": "550e8400-e29b-41d4-a716-446655440000",
                            "zoho_lead_id": "ZOHO123",
                            "full_name": "John Doe Updated",
                            "contact_number": "+1234567890",
                            "email": "<EMAIL>",
                            "location": "New York",
                            "lead_source": "Website",
                            "franchise_preference": "Food",
                            "budget_preference": 75000.00,
                            "qualification_status": "qualified",
                            "created_at": "2024-01-01T00:00:00Z",
                            "updated_at": "2024-01-01T12:00:00Z"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def update_lead(
    lead_id: Annotated[str, Path(description="Unique lead identifier", example="550e8400-e29b-41d4-a716-446655440000")],
    lead_data: LeadUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    lead_service: LeadService = Depends(get_lead_service)
):
    """
    Update an existing lead with validation and authentication.
    
    This endpoint updates a lead with the provided data. Only provided fields
    will be updated, maintaining data integrity.
    """
    try:
        lead = await lead_service.update_lead(lead_id, lead_data)
        
        if not lead:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Lead Not Found",
                message_description=f"Lead with ID {lead_id} not found",
                status_code=404
            )

        response_data = await lead_service._lead_to_response(lead)

        return LeadSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Lead Updated",
                description="Lead updated successfully"
            ),
            data=response_data
        )
        
    except HTTPException:
        # Re-raise HTTPExceptions from service layer (they have proper error messages)
        raise
    except Exception as e:
        logger.error(f"Unexpected error in update_lead API: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while updating the lead"
        )


@router.delete(
    "/{lead_id}",
    response_model=LeadDeleteSuccessResponse,
    summary="Delete Lead",
    description="Delete a lead with authentication and authorization checks",
    responses={
        200: {
            "description": "Lead deleted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Lead Deleted",
                            "description": "Lead deleted successfully"
                        },
                        "data": {
                            "lead_id": "550e8400-e29b-41d4-a716-446655440000",
                            "deleted_at": "2024-01-01T12:00:00Z",
                            "message": "Lead has been permanently deleted"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def delete_lead(
    lead_id: Annotated[str, Path(description="Unique lead identifier", example="550e8400-e29b-41d4-a716-446655440000")],
    current_user: Dict[str, Any] = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    lead_service: LeadService = Depends(get_lead_service)
):
    """
    Delete a lead with authentication and authorization checks.
    
    This endpoint permanently deletes a lead after verifying user permissions.
    The operation cannot be undone.
    """
    try:
        success = await lead_service.delete_lead(lead_id)
        
        if not success:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Lead Not Found",
                message_description=f"Lead with ID {lead_id} not found",
                status_code=404
            )

        return LeadDeleteSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Lead Deleted",
                description="Lead deleted successfully"
            ),
            data={
                "lead_id": lead_id,
                "deleted_at": "2024-01-01T12:00:00Z",
                "message": "Lead has been permanently deleted"
            }
        )
        
    except Exception as e:
        logger.error(f"Error in delete_lead: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete lead"
        )








@router.post(
    "/bulk-upload",
    response_model=BulkUploadSuccessResponse,
    summary="Bulk Upload Leads",
    description="Upload leads from CSV file with duplicate detection",
    responses={
        200: {
            "description": "Bulk upload completed successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Bulk Upload Completed",
                            "description": "Leads uploaded successfully"
                        },
                        "data": {
                            "total_processed": 100,
                            "successful_imports": 95,
                            "duplicates_found": 3,
                            "errors_found": 2,
                            "duplicates": ["Row 5: Duplicate contact number +1234567890"],
                            "errors": ["Row 10: Invalid budget_preference value"]
                        }
                    }
                }
            }
        },
        400: {"description": "Invalid file format"},
        422: {"description": "Missing required headers"},
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def bulk_upload_leads(
    file: UploadFile = File(..., description="CSV file containing lead data"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    lead_service: LeadService = Depends(get_lead_service)
):
    """
    Bulk upload leads from CSV file.

    Expected CSV headers:
    - full_name (required)
    - phone (optional)
    - mobile (optional)
    - email (optional)
    - location (optional)
    - lead_source (optional)
    - brand_preference (optional)
    - budget_preference (optional)
    - qualification_status (optional, defaults to 'new')
    - status (optional, defaults to 'new')
    - is_deleted (optional, defaults to false, accepts: true/false, 1/0, yes/no)

    The endpoint performs duplicate detection based on phone, mobile and email.
    """
    try:
        result = await lead_service.bulk_upload_leads(file=file)

        return BulkUploadSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Bulk Upload Completed",
                description=f"Processed {result['total_processed']} rows, imported {result['successful_imports']} leads"
            ),
            data=result
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk_upload_leads: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to process bulk upload"
        )


@router.get(
    "/{lead_id}/history",
    response_model=CommunicationHistorySuccessResponse,
    summary="Get Lead Communication History",
    description="Retrieve communication history for a specific lead",
    responses={
        200: {
            "description": "Communication history retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Communication History Retrieved",
                            "description": "Communication history retrieved successfully"
                        },
                        "data": {
                            "communications": [
                                {
                                    "id": "550e8400-e29b-41d4-a716-446655440000",
                                    "communication_type": "phone",
                                    "subject": "Follow-up call",
                                    "content": "Discussed franchise opportunities",
                                    "direction": "outbound",
                                    "user_id": "123e4567-e89b-12d3-a456-426614174000",
                                    "created_at": "2024-01-01T10:00:00Z",
                                    "updated_at": "2024-01-01T10:00:00Z"
                                }
                            ],
                            "total_count": 1
                        }
                    }
                }
            }
        },
        404: {"description": "Lead not found"},
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_lead_communication_history(
    lead_id: Annotated[str, Path(description="Unique lead identifier", example="550e8400-e29b-41d4-a716-446655440000")],
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get communication history for a specific lead.

    Returns all communications (emails, phone calls, notes, etc.)
    associated with the lead, ordered by creation date (newest first).
    """
    try:
        lead_service = LeadService(db)
        communications = await lead_service.get_lead_communications(lead_id)

        return CommunicationHistorySuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Communication History Retrieved",
                description="Communication history retrieved successfully"
            ),
            data={
                "communications": communications,
                "total_count": len(communications)
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_lead_communication_history: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve communication history"
        )
