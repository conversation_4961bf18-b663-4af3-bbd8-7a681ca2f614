"""
Question Bank API Endpoints
REST API endpoints for question bank management
"""

from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, Query, HTTPException, Path
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database.connection import get_db
from app.services.question_bank_service import QuestionBankService
from app.schemas.question_bank import (
    QuestionCreateRequest,
    QuestionUpdateRequest,
    QuestionReorderRequest,
    QuestionToggleRequest,
    QuestionDeleteRequest,
    FranchisorDropdownResponse,
    QuestionResponse,
    QuestionListResponse,
    QuestionListPaginatedResponse
)
from app.core.app_rules import ApiResponse
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.logging import logger
import uuid

router = APIRouter()


@router.get(
    "/franchisors/dropdown",
    response_model=ApiResponse,
    summary="Get Franchisors for Dropdown",
    description="Retrieve franchisors for dropdown selection with name, id, and category",
    responses={
        200: {
            "description": "Franchisors retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "Franchisors Retrieved",
                        "description": "Successfully retrieved 5 franchisors for dropdown",
                        "details": {
                            "franchisors": [
                                {
                                    "id": 1,
                                    "name": "McDonald's",
                                    "category": "Food & Beverage"
                                },
                                {
                                    "id": 2,
                                    "name": "Subway",
                                    "category": "Food & Beverage"
                                }
                            ]
                        }
                    }
                }
            }
        },
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def get_franchisors_for_dropdown(
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get franchisors for dropdown selection.
    
    Returns franchisors with id, name, and category for use in question creation.
    """
    logger.info("API: get_franchisors_for_dropdown called", extra={"message_key": "FRANCHISOR_DROPDOWN_REQUEST"})
    
    service = QuestionBankService(db)
    return await service.get_franchisors_for_dropdown()


@router.post(
    "/create",
    response_model=ApiResponse,
    summary="Create New Question",
    description="Create a new question with auto-calculated order. Order is automatically set based on existing questions for the franchisor.",
    responses={
        200: {
            "description": "Question created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "Question Created",
                        "description": "Question created successfully with auto-calculated order",
                        "details": {
                            "id": 1,
                            "franchisor_id": 1,
                            "franchisor_name": "McDonald's",
                            "category": "Food & Beverage",
                            "question_text": "What is your investment budget?",
                            "question_internal_text": "What is your investment budget?",
                            "question_type": "multiple_choice",
                            "order": 1,
                            "is_active": "True",
                            "created_at": "2024-01-15T10:30:00Z",
                            "updated_at": "2024-01-15T10:30:00Z"
                        }
                    }
                }
            }
        },
        400: {"description": "Invalid franchisor or validation error"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def create_question(
    question_data: QuestionCreateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Create a new question.
    
    - **franchisor_id**: ID of the franchisor this question belongs to
    - **question_text**: The question text (will also be stored as question_internal_text)
    - **question_type**: Type of question (e.g., "text", "multiple_choice", "yes_no")

    
    Order is automatically calculated based on existing questions for the franchisor.
    """
    logger.info("API: create_question called", extra={"message_key": "QUESTION_CREATE_REQUEST", "franchisor_id": question_data.franchisor_id})
    
    service = QuestionBankService(db)
    return await service.create_question(question_data)


@router.put(
    "/{question_id}/update",
    response_model=ApiResponse,
    summary="Update Question",
    description="Update a question. Order and question_internal_text cannot be modified.",
    responses={
        200: {
            "description": "Question updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "Question Updated",
                        "description": "Question updated successfully",
                        "details": {
                            "id": "550e8400-e29b-41d4-a716-************",
                            "franchisor_id": "550e8400-e29b-41d4-a716-************",
                            "franchisor_name": "McDonald's",
                            "category": "Food & Beverage",
                            "question_text": "Updated question text?",
                            "question_internal_text": "What is your investment budget?",
                            "question_type": "multiple_choice",
                            "order": 1,
                            "is_active": "True",
                            "created_at": "2024-01-15T10:30:00Z",
                            "updated_at": "2024-01-15T11:00:00Z"
                        }
                    }
                }
            }
        },
        404: {"description": "Question not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def update_question(
    question_data: QuestionUpdateRequest,
    question_id: uuid.UUID = Path(..., description="Question ID to update"),
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Update a question.
    
    - **question_text**: Updated question text
    - **question_type**: Updated question type

    
    Note: Order and question_internal_text cannot be modified through this endpoint.
    """
    logger.info("API: update_question called", extra={"message_key": "QUESTION_UPDATE_REQUEST", "question_id": question_id})
    
    service = QuestionBankService(db)
    return await service.update_question(question_id, question_data)


@router.delete(
    "/{question_id}/delete",
    response_model=ApiResponse,
    summary="Delete Question",
    description="Soft delete a question and automatically reorder subsequent questions. When a question is deleted, all questions with higher order_sequence in the same franchisor will have their order decremented by 1.",
    responses={
        200: {
            "description": "Question deleted successfully and subsequent questions reordered",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "Question Deleted",
                        "description": "Question at position 5 deleted successfully. Subsequent questions have been reordered automatically.",
                        "details": {
                            "question_id": "550e8400-e29b-41d4-a716-************",
                            "deleted_order": 5,
                            "franchisor_id": "3466656e-2380-4c08-8e5c-b590fe597cce",
                            "reordered": True
                        }
                    }
                }
            }
        },
        404: {"description": "Question not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def delete_question(
    question_id: uuid.UUID = Path(..., description="Question ID to delete"),
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Soft delete a question and automatically reorder subsequent questions.

    The question is marked as deleted but remains in the database for audit purposes.
    All questions with higher order_sequence in the same franchisor will have their
    order decremented by 1 to maintain sequential ordering.

    Example: If you delete question with order 5 out of 10 questions:
    - Questions 1-4: remain unchanged
    - Question 5: deleted
    - Questions 6-10: become 5-9 respectively
    """
    logger.info("API: delete_question called", extra={"message_key": "QUESTION_DELETE_REQUEST", "question_id": question_id})
    
    service = QuestionBankService(db)
    return await service.delete_question(question_id)


@router.patch(
    "/{question_id}/toggle-status",
    response_model=ApiResponse,
    summary="Toggle Question Status",
    description="Toggle question status between active and inactive",
    responses={
        200: {
            "description": "Question status toggled successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "Question Status Toggled",
                        "description": "Question activated successfully",
                        "details": {
                            "id": "550e8400-e29b-41d4-a716-************",
                            "franchisor_id": "550e8400-e29b-41d4-a716-************",
                            "franchisor_name": "McDonald's",
                            "category": "Food & Beverage",
                            "question_text": "What is your investment budget?",
                            "question_internal_text": "What is your investment budget?",
                            "question_type": "multiple_choice",

                            "order": 1,
                            "is_active": "True",
                            "created_at": "2024-01-15T10:30:00Z",
                            "updated_at": "2024-01-15T11:00:00Z"
                        }
                    }
                }
            }
        },
        404: {"description": "Question not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def toggle_question_status(
    question_id: uuid.UUID = Path(..., description="Question ID to toggle"),
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Toggle question status between active and inactive.
    
    If the question is currently active, it will be set to inactive and vice versa.
    """
    logger.info("API: toggle_question_status called", extra={"message_key": "QUESTION_TOGGLE_REQUEST", "question_id": question_id})
    
    service = QuestionBankService(db)
    return await service.toggle_question_status(question_id)


@router.get(
    "/franchisor/{franchisor_id}",
    response_model=ApiResponse,
    summary="Get Questions by Franchisor",
    description="Retrieve all questions for a specific franchisor with franchisor details",
    responses={
        200: {
            "description": "Questions retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "Questions Retrieved",
                        "description": "Successfully retrieved 3 questions for franchisor",
                        "details": {
                            "questions": [
                                {
                                    "id": "550e8400-e29b-41d4-a716-************",
                                    "franchisor_id": "550e8400-e29b-41d4-a716-************",
                                    "franchisor_name": "McDonald's",
                                    "category": "Food & Beverage",
                                    "question_text": "What is your investment budget?",
                                    "question_internal_text": "What is your investment budget?",
                                    "question_type": "multiple_choice",
                                    "order": 1,
                                    "is_active": True,
                                    "created_at": "2024-01-15T10:30:00Z",
                                    "updated_at": "2024-01-15T10:30:00Z"
                                }
                            ]
                        }
                    }
                }
            }
        },
        400: {"description": "Invalid franchisor"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def get_questions_by_franchisor(
    franchisor_id: uuid.UUID = Path(..., description="Franchisor ID"),
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get all questions for a specific franchisor.
    
    Returns questions ordered by their sequence order, including franchisor name and category.
    """
    logger.info("API: get_questions_by_franchisor called", extra={"message_key": "QUESTIONS_BY_FRANCHISOR_REQUEST", "franchisor_id": franchisor_id})
    
    service = QuestionBankService(db)
    return await service.get_questions_by_franchisor(franchisor_id)


@router.get(
    "/list",
    response_model=ApiResponse,
    summary="List All Questions",
    description="Retrieve all questions with pagination and filtering options",
    responses={
        200: {
            "description": "Questions retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "Questions Retrieved",
                        "description": "Successfully retrieved 10 questions",
                        "details": {
                            "items": [
                                {
                                    "id": "550e8400-e29b-41d4-a716-************",
                                    "franchisor_id": "550e8400-e29b-41d4-a716-************",
                                    "franchisor_name": "McDonald's",
                                    "category": "Food & Beverage",
                                    "question_text": "What is your investment budget?",
                                    "question_type": "multiple_choice",
                                    "order": 1,
                                    "is_active": True,
                                    "created_at": "2024-01-15T10:30:00Z"
                                }
                            ],
                            "total": 100,
                            "page": 1,
                            "size": 10,
                            "pages": 10
                        }
                    }
                }
            }
        },
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def list_all_questions(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Page size"),
    search: Optional[str] = Query(None, description="Search term for question text"),
    franchisor_id: Optional[uuid.UUID] = Query(None, description="Filter by franchisor ID"),
    is_active: Optional[bool] = Query(None, description="Filter by status (True for active, False for inactive)"),
    sort_by: Optional[str] = Query("created_at", description="Sort by field (question_text, created_at, order_sequence)"),
    sort_order: Optional[str] = Query("desc", description="Sort order (asc, desc)"),
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    List all questions with pagination, filtering, and sorting.

    - **page**: Page number (default: 1)
    - **size**: Page size (default: 10, max: 100)
    - **search**: Search term to filter questions by text
    - **franchisor_id**: Filter questions by specific franchisor
    - **status**: Filter by question status (active/inactive)
    - **sort_by**: Sort by field (question_text, created_at, order_sequence)
    - **sort_order**: Sort order (asc, desc)
    """
    logger.info("API: list_all_questions called", extra={
        "message_key": "QUESTIONS_LIST_REQUEST",
        "page": page,
        "size": size,
        "search": search,
        "franchisor_id": franchisor_id,
        "is_active": is_active
    })
    
    service = QuestionBankService(db)
    # Convert is_active boolean to string status
    status = "active" if is_active else "inactive" if is_active is not None else None
    return await service.get_all_questions_paginated(
        page=page,
        size=size,
        search=search,
        franchisor_id=franchisor_id,
        is_active=is_active,
        sort_by=sort_by,
        sort_order=sort_order
    )


@router.post(
    "/reorder",
    response_model=ApiResponse,
    summary="Reorder Questions",
    description="Reorder questions for a specific franchisor. Provide new order for each question.",
    responses={
        200: {
            "description": "Questions reordered successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "Questions Reordered",
                        "description": "Successfully reordered 3 questions",
                        "details": {
                            "franchisor_id": 1,
                            "reordered_questions": 3
                        }
                    }
                }
            }
        },
        400: {"description": "Invalid order or validation error"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def reorder_questions(
    reorder_data: QuestionReorderRequest,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Reorder questions for a specific franchisor.
    
    - **franchisor_id**: ID of the franchisor whose questions to reorder
    - **questions**: Array of objects with question_id and new order
    
    Example:
    ```json
    {
        "franchisor_id": 1,
        "questions": [
            {"question_id": 1, "order": 3},
            {"question_id": 2, "order": 1},
            {"question_id": 3, "order": 2}
        ]
    }
    ```
    
    All questions must belong to the specified franchisor and order numbers must be unique.
    """
    logger.info("API: reorder_questions called", extra={
        "message_key": "QUESTIONS_REORDER_REQUEST",
        "franchisor_id": reorder_data.franchisor_id,
        "question_count": len(reorder_data.questions)
    })
    
    service = QuestionBankService(db)
    return await service.reorder_questions(reorder_data)


@router.get(
    "/franchisor/{franchisor_id}/validate-order",
    response_model=ApiResponse,
    summary="Validate Question Order Integrity",
    description="Check if question order sequences are valid (sequential, no duplicates, no gaps)",
    responses={
        200: {
            "description": "Validation completed",
            "content": {
                "application/json": {
                    "examples": {
                        "valid": {
                            "summary": "Valid order sequence",
                            "value": {
                                "success": True,
                                "title": "Order Sequence Valid",
                                "description": "All 5 questions have valid sequential order numbers",
                                "details": {
                                    "is_valid": True,
                                    "total_questions": 5,
                                    "current_orders": [1, 2, 3, 4, 5],
                                    "expected_orders": [1, 2, 3, 4, 5],
                                    "issues": []
                                }
                            }
                        },
                        "invalid": {
                            "summary": "Invalid order sequence",
                            "value": {
                                "success": False,
                                "title": "Order Sequence Issues Found",
                                "description": "Found 2 issues with question order sequence",
                                "details": {
                                    "is_valid": False,
                                    "total_questions": 5,
                                    "current_orders": [1, 3, 3, 5, 7],
                                    "expected_orders": [1, 2, 3, 4, 5],
                                    "issues": ["Duplicate order numbers found: [3]", "Missing order numbers: [2, 4]", "Invalid order numbers: [7]"]
                                }
                            }
                        }
                    }
                }
            }
        },
        404: {"description": "Franchisor not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def validate_question_order_integrity(
    franchisor_id: uuid.UUID = Path(..., description="Franchisor ID"),
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Validate question order sequence integrity for a franchisor.

    Checks for:
    - Sequential order numbers (1, 2, 3, 4...)
    - No duplicate order numbers
    - No gaps in the sequence
    """
    logger.info("API: validate_question_order_integrity called", extra={
        "message_key": "VALIDATE_ORDER_REQUEST",
        "franchisor_id": franchisor_id
    })

    service = QuestionBankService(db)
    return await service.validate_question_order_integrity(franchisor_id)


@router.post(
    "/franchisor/{franchisor_id}/fix-order",
    response_model=ApiResponse,
    summary="Fix Question Order Integrity",
    description="Automatically fix question order sequences to be sequential without gaps or duplicates",
    responses={
        200: {
            "description": "Order sequence fixed successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "title": "Order Sequence Fixed",
                        "description": "Successfully fixed order sequence for 5 questions",
                        "details": {
                            "before": {
                                "is_valid": False,
                                "total_questions": 5,
                                "current_orders": [1, 3, 3, 5, 7],
                                "issues": ["Duplicate order numbers found: [3]"]
                            },
                            "after": {
                                "is_valid": True,
                                "total_questions": 5,
                                "current_orders": [1, 2, 3, 4, 5],
                                "issues": []
                            },
                            "fixed": True
                        }
                    }
                }
            }
        },
        404: {"description": "Franchisor not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    }
)
async def fix_question_order_integrity(
    franchisor_id: uuid.UUID = Path(..., description="Franchisor ID"),
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Automatically fix question order sequence integrity.

    This will:
    - Remove duplicate order numbers
    - Fill gaps in the sequence
    - Ensure sequential ordering (1, 2, 3, 4...)

    Questions are reordered based on their current order_sequence and creation time.
    """
    logger.info("API: fix_question_order_integrity called", extra={
        "message_key": "FIX_ORDER_REQUEST",
        "franchisor_id": franchisor_id
    })

    service = QuestionBankService(db)
    return await service.fix_question_order_integrity(franchisor_id)