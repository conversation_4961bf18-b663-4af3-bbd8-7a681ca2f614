"""Lead service for business logic"""
import uuid
import csv
import io
from typing import List, Optional, Tuple, Dict, Any
from decimal import Decimal, InvalidOperation
from datetime import datetime
from sqlalchemy import select, update, delete, and_, or_, desc, func
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from fastapi import HTTPException, UploadFile

from app.models.lead import Lead, Communication
from app.models.lead_reference import LeadSource, LeadStatus as LeadStatusModel
from app.schemas.lead import (
    LeadCreateRequest,
    LeadUpdateRequest,
    LeadResponse
)
from app.core.logging import logger


class LeadService:
    """Service for lead-related business logic"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    def _generate_lead_id(self) -> str:
        """Generate a unique lead ID"""
        return str(uuid.uuid4())

    async def _lead_to_response(self, lead: Lead) -> LeadResponse:
        """Convert Lead model to LeadResponse with related data"""
        # Get lead source details
        lead_source_name = None
        if lead.lead_source_id and lead.lead_source_rel:
            lead_source_name = lead.lead_source_rel.name

        # Get lead status details
        lead_status_name = "Unknown"
        lead_status_colour = "#000000"
        if lead.lead_status_id and lead.lead_status_rel:
            lead_status_name = lead.lead_status_rel.name
            lead_status_colour = lead.lead_status_rel.colour

        return LeadResponse(
            id=str(lead.id),
            zoho_lead_id=lead.zoho_lead_id,
            first_name=lead.first_name,
            last_name=lead.last_name,
            phone=lead.phone,
            mobile=lead.mobile,
            email=lead.email,
            location=lead.location,
            postal_code=lead.postal_code,
            lead_source_id=str(lead.lead_source_id) if lead.lead_source_id else None,
            lead_source_name=lead_source_name,
            lead_status_id=str(lead.lead_status_id),
            lead_status_name=lead_status_name,
            lead_status_colour=lead_status_colour,
            brand_preference=str(lead.brand_preference) if lead.brand_preference else None,
            budget_preference=lead.budget_preference,
            franchise_interested_in=lead.franchise_interested_in,
            looking_for_business_opportunity_since=lead.looking_for_business_opportunity_since,
            skills=lead.skills,
            looking_to_be_owner_operator=lead.looking_to_be_owner_operator,
            when_looking_to_start=lead.when_looking_to_start,
            ethnic_background=lead.ethnic_background,
            funds_to_invest=lead.funds_to_invest,
            eoi_nda_link=lead.eoi_nda_link,
            work_background=lead.work_background,
            motivation_to_enquire=lead.motivation_to_enquire,
            funds_available=lead.funds_available,
            motivation=lead.motivation,
            have_run_business_before=lead.have_run_business_before,
            have_mortgage=lead.have_mortgage,
            high_net_worth=lead.high_net_worth,
            is_active=lead.is_active,
            is_deleted=lead.is_deleted,
            created_at=lead.created_at,
            updated_at=lead.updated_at
        )
    
    async def create_lead(self, lead_data: LeadCreateRequest, created_by: str = None) -> Lead:
        """Create a new lead with updated database structure"""
        try:
            # Convert UUIDs if provided
            brand_preference_uuid = None
            if lead_data.brand_preference:
                try:
                    brand_preference_uuid = uuid.UUID(lead_data.brand_preference)
                except ValueError:
                    logger.warning(f"Invalid UUID format for brand_preference: {lead_data.brand_preference}")
                    brand_preference_uuid = None

            lead_source_uuid = None
            if lead_data.lead_source_id:
                try:
                    lead_source_uuid = uuid.UUID(lead_data.lead_source_id)
                except ValueError:
                    logger.warning(f"Invalid UUID format for lead_source_id: {lead_data.lead_source_id}")
                    lead_source_uuid = None

            lead_status_uuid = None
            if lead_data.lead_status_id:
                try:
                    lead_status_uuid = uuid.UUID(lead_data.lead_status_id)
                except ValueError:
                    logger.warning(f"Invalid UUID format for lead_status_id: {lead_data.lead_status_id}")
                    lead_status_uuid = None

            # Validate lead source exists if provided
            if lead_source_uuid:
                from app.models.lead_reference import LeadSource
                source_query = select(LeadSource).where(
                    LeadSource.id == lead_source_uuid,
                    LeadSource.is_active == True,
                    LeadSource.is_deleted == False
                )
                result = await self.db.execute(source_query)
                lead_source_exists = result.scalar_one_or_none()
                if not lead_source_exists:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Lead source with ID {lead_data.lead_source_id} does not exist or is inactive"
                    )

            # Validate lead status exists if provided, otherwise get default
            if lead_status_uuid:
                from app.models.lead_reference import LeadStatus as LeadStatusModel
                status_query = select(LeadStatusModel).where(
                    LeadStatusModel.id == lead_status_uuid,
                    LeadStatusModel.is_active == True,
                    LeadStatusModel.is_deleted == False
                )
                result = await self.db.execute(status_query)
                lead_status_exists = result.scalar_one_or_none()
                if not lead_status_exists:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Lead status with ID {lead_data.lead_status_id} does not exist or is inactive"
                    )
            else:
                # Get default "New Lead" status
                from app.models.lead_reference import LeadStatus as LeadStatusModel
                default_status_query = select(LeadStatusModel).where(
                    LeadStatusModel.name == "New Lead",
                    LeadStatusModel.is_active == True,
                    LeadStatusModel.is_deleted == False
                )
                result = await self.db.execute(default_status_query)
                default_status = result.scalar_one_or_none()
                if default_status:
                    lead_status_uuid = default_status.id
                else:
                    raise HTTPException(
                        status_code=500,
                        detail="Default lead status 'New Lead' not found. Please contact administrator."
                    )

            # Validate brand preference exists if provided
            if brand_preference_uuid:
                from app.models.franchisor import Franchisor
                brand_query = select(Franchisor).where(
                    Franchisor.id == brand_preference_uuid,
                    Franchisor.is_active == True,
                    Franchisor.is_deleted == False
                )
                result = await self.db.execute(brand_query)
                brand_exists = result.scalar_one_or_none()
                if not brand_exists:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Brand preference with ID {lead_data.brand_preference} does not exist or is inactive"
                    )

            lead_kwargs = dict(
                id=uuid.uuid4(),
                zoho_lead_id=lead_data.zoho_lead_id,
                first_name=lead_data.first_name,
                last_name=lead_data.last_name,
                phone=lead_data.phone,
                mobile=lead_data.mobile,
                email=lead_data.email,
                location=lead_data.location,
                postal_code=lead_data.postal_code,
                lead_source_id=lead_source_uuid,
                lead_status_id=lead_status_uuid,
                brand_preference=brand_preference_uuid,
                budget_preference=lead_data.budget_preference,
                # Franchise buyer information fields
                franchise_interested_in=lead_data.franchise_interested_in,
                looking_for_business_opportunity_since=lead_data.looking_for_business_opportunity_since,
                skills=lead_data.skills,
                looking_to_be_owner_operator=lead_data.looking_to_be_owner_operator,
                when_looking_to_start=lead_data.when_looking_to_start,
                ethnic_background=lead_data.ethnic_background,
                funds_to_invest=lead_data.funds_to_invest,
                eoi_nda_link=lead_data.eoi_nda_link,
                work_background=lead_data.work_background,
                motivation_to_enquire=lead_data.motivation_to_enquire,
                funds_available=lead_data.funds_available,
                motivation=lead_data.motivation,
                have_run_business_before=lead_data.have_run_business_before,
                have_mortgage=lead_data.have_mortgage,
                high_net_worth=lead_data.high_net_worth,
                is_active=lead_data.is_active if lead_data.is_active is not None else True,
                is_deleted=False
            )

            # If the Lead model supports created_by, set it
            if hasattr(Lead, "created_by") and created_by:
                lead_kwargs["created_by"] = created_by

            lead = Lead(**lead_kwargs)
            self.db.add(lead)
            await self.db.commit()
            await self.db.refresh(lead)
            logger.info(f"Created lead: {lead.id}")

            # Fetch the lead with related data loaded
            return await self.get_lead_by_id(str(lead.id))
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Database integrity error creating lead: {e}")
            if "duplicate key" in str(e).lower():
                if "email" in str(e).lower():
                    raise HTTPException(
                        status_code=409,
                        detail="A lead with this email address already exists"
                    )
                elif "phone" in str(e).lower():
                    raise HTTPException(
                        status_code=409,
                        detail="A lead with this phone number already exists"
                    )
                elif "zoho_lead_id" in str(e).lower():
                    raise HTTPException(
                        status_code=409,
                        detail="A lead with this Zoho ID already exists"
                    )
                else:
                    raise HTTPException(
                        status_code=409,
                        detail="A lead with this information already exists"
                    )
            raise HTTPException(
                status_code=400,
                detail="Invalid lead data provided"
            )
        except ValueError as e:
            await self.db.rollback()
            logger.error(f"Validation error creating lead: {e}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid data format: {str(e)}"
            )
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error creating lead: {e}")
            raise HTTPException(
                status_code=500,
                detail="Database error occurred while creating lead"
            )
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error creating lead: {e}")
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while creating the lead"
            )
    
    async def get_lead_by_id(self, lead_id: str, user_role: str = None, user_id: str = None, include_deleted: bool = False) -> Optional[Lead]:
        """Get a lead by ID with optional permission checking and related data"""
        try:
            query = select(Lead).options(
                selectinload(Lead.lead_source_rel),
                selectinload(Lead.lead_status_rel),
                selectinload(Lead.franchisor)
            ).where(Lead.id == uuid.UUID(lead_id))

            # Exclude soft-deleted leads unless explicitly requested
            if not include_deleted:
                query = query.where(Lead.is_deleted == False)

            result = await self.db.execute(query)
            lead = result.scalar_one_or_none()

            # Admin can see all leads, regular users can only see their assigned leads
            if lead and user_role and user_role != "ADMIN":
                # For now, allow all users to see all leads
                # TODO: Implement proper lead assignment logic
                pass

            return lead
        except ValueError as e:
            logger.error(f"Invalid lead ID format: {e}")
            raise HTTPException(
                status_code=400,
                detail="Invalid lead ID format"
            )
        except SQLAlchemyError as e:
            logger.error(f"Database error getting lead: {e}")
            raise HTTPException(
                status_code=500,
                detail="Database error occurred while retrieving lead"
            )
        except Exception as e:
            logger.error(f"Unexpected error getting lead: {e}")
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while retrieving the lead"
            )
    
    async def get_leads(
        self,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None,
        search: Optional[str] = None,
        lead_source: Optional[str] = None,
        created_from: Optional[datetime] = None,
        created_to: Optional[datetime] = None,
        sort: Optional[str] = None,
        user_role: str = None,
        user_id: str = None
    ) -> Tuple[List[LeadResponse], int]:
        """Get all leads with pagination, filtering, and permission checking"""
        try:
            # Build query with eager loading
            query = select(Lead).options(
                selectinload(Lead.lead_source_rel),
                selectinload(Lead.lead_status_rel),
                selectinload(Lead.franchisor)
            )

            # Apply filters
            filters = []

            # Always exclude soft-deleted leads
            filters.append(Lead.is_deleted == False)

            if status:
                filters.append(Lead.qualification_status == status)
            if lead_source:
                filters.append(Lead.lead_source.ilike(f"%{lead_source}%"))
            if created_from:
                filters.append(Lead.created_at >= created_from)
            if created_to:
                filters.append(Lead.created_at <= created_to)
            if search:
                # Enhanced full-text search across multiple fields
                search_filter = or_(
                    Lead.first_name.ilike(f"%{search}%"),
                    Lead.last_name.ilike(f"%{search}%"),
                    Lead.email.ilike(f"%{search}%"),
                    Lead.phone.ilike(f"%{search}%"),
                    Lead.mobile.ilike(f"%{search}%"),
                    Lead.location.ilike(f"%{search}%")
                )
                filters.append(search_filter)

            # Apply all filters
            query = query.where(and_(*filters))
            
            # Get total count
            count_query = select(func.count(Lead.id))
            if filters:
                count_query = count_query.where(and_(*filters))
            
            count_result = await self.db.execute(count_query)
            total_count = count_result.scalar()
            
            # Apply sorting
            if sort:
                if sort == "name_asc":
                    query = query.order_by(Lead.first_name.asc(), Lead.last_name.asc())
                elif sort == "name_desc":
                    query = query.order_by(Lead.first_name.desc(), Lead.last_name.desc())
                elif sort == "created_asc":
                    query = query.order_by(Lead.created_at.asc())
                elif sort == "created_desc":
                    query = query.order_by(Lead.created_at.desc())
                else:
                    query = query.order_by(desc(Lead.created_at))
            else:
                query = query.order_by(desc(Lead.created_at))

            # Apply pagination
            query = query.offset(skip).limit(limit)
            
            # Execute query
            result = await self.db.execute(query)
            leads = result.scalars().all()
            
            # Convert to response models using helper method
            lead_responses = []
            for lead in leads:
                lead_response = await self._lead_to_response(lead)
                lead_responses.append(lead_response)
            
            return lead_responses, total_count
            
        except ValueError as e:
            logger.error(f"Invalid parameter in get_leads: {e}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid parameter provided: {str(e)}"
            )
        except SQLAlchemyError as e:
            logger.error(f"Database error getting leads: {e}")
            raise HTTPException(
                status_code=500,
                detail="Database error occurred while retrieving leads"
            )
        except Exception as e:
            logger.error(f"Unexpected error getting leads: {e}")
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while retrieving leads"
            )
    
    async def update_lead(self, lead_id: str, lead_data: LeadUpdateRequest, user_role: str = None, user_id: str = None) -> Optional[Lead]:
        """Update a lead with comprehensive validation and error handling"""
        try:
            # Validate lead_id format
            try:
                lead_uuid = uuid.UUID(lead_id)
            except ValueError as e:
                logger.error(f"Invalid lead ID format: {lead_id}")
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid lead ID format: {lead_id}"
                )

            # Get existing lead
            lead = await self.get_lead_by_id(lead_id, user_role, user_id)
            if not lead:
                logger.warning(f"Lead not found for update: {lead_id}")
                raise HTTPException(
                    status_code=404,
                    detail=f"Lead with ID {lead_id} not found"
                )

            # Prepare update data, excluding None values
            update_data = {}

            # Basic information fields
            if lead_data.first_name is not None:
                update_data["first_name"] = lead_data.first_name.strip() if lead_data.first_name else None
            if lead_data.last_name is not None:
                update_data["last_name"] = lead_data.last_name.strip() if lead_data.last_name else None
            if lead_data.phone is not None:
                update_data["phone"] = lead_data.phone.strip() if lead_data.phone else None
            if lead_data.mobile is not None:
                update_data["mobile"] = lead_data.mobile.strip() if lead_data.mobile else None
            if lead_data.email is not None:
                update_data["email"] = lead_data.email.strip().lower() if lead_data.email else None
            if lead_data.location is not None:
                update_data["location"] = lead_data.location.strip() if lead_data.location else None
            if lead_data.postal_code is not None:
                update_data["postal_code"] = lead_data.postal_code.strip() if lead_data.postal_code else None

            # Reference fields with UUID validation and existence check
            if lead_data.lead_source_id is not None:
                if lead_data.lead_source_id:
                    try:
                        lead_source_uuid = uuid.UUID(lead_data.lead_source_id)
                        # Validate lead source exists
                        from app.models.lead_reference import LeadSource
                        source_query = select(LeadSource).where(
                            LeadSource.id == lead_source_uuid,
                            LeadSource.is_active == True,
                            LeadSource.is_deleted == False
                        )
                        result = await self.db.execute(source_query)
                        lead_source_exists = result.scalar_one_or_none()
                        if not lead_source_exists:
                            raise HTTPException(
                                status_code=400,
                                detail=f"Lead source with ID {lead_data.lead_source_id} does not exist or is inactive"
                            )
                        update_data["lead_source_id"] = lead_source_uuid
                    except ValueError:
                        logger.error(f"Invalid lead_source_id format: {lead_data.lead_source_id}")
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid lead source ID format: {lead_data.lead_source_id}"
                        )
                else:
                    update_data["lead_source_id"] = None

            if lead_data.lead_status_id is not None:
                if lead_data.lead_status_id:
                    try:
                        lead_status_uuid = uuid.UUID(lead_data.lead_status_id)
                        # Validate lead status exists
                        from app.models.lead_reference import LeadStatus as LeadStatusModel
                        status_query = select(LeadStatusModel).where(
                            LeadStatusModel.id == lead_status_uuid,
                            LeadStatusModel.is_active == True,
                            LeadStatusModel.is_deleted == False
                        )
                        result = await self.db.execute(status_query)
                        lead_status_exists = result.scalar_one_or_none()
                        if not lead_status_exists:
                            raise HTTPException(
                                status_code=400,
                                detail=f"Lead status with ID {lead_data.lead_status_id} does not exist or is inactive"
                            )
                        update_data["lead_status_id"] = lead_status_uuid
                    except ValueError:
                        logger.error(f"Invalid lead_status_id format: {lead_data.lead_status_id}")
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid lead status ID format: {lead_data.lead_status_id}"
                        )
                else:
                    update_data["lead_status_id"] = None

            if lead_data.brand_preference is not None:
                if lead_data.brand_preference:
                    try:
                        brand_preference_uuid = uuid.UUID(lead_data.brand_preference)
                        # Validate brand preference exists
                        from app.models.franchisor import Franchisor
                        brand_query = select(Franchisor).where(
                            Franchisor.id == brand_preference_uuid,
                            Franchisor.is_active == True,
                            Franchisor.is_deleted == False
                        )
                        result = await self.db.execute(brand_query)
                        brand_exists = result.scalar_one_or_none()
                        if not brand_exists:
                            raise HTTPException(
                                status_code=400,
                                detail=f"Brand preference with ID {lead_data.brand_preference} does not exist or is inactive"
                            )
                        update_data["brand_preference"] = brand_preference_uuid
                    except ValueError:
                        logger.error(f"Invalid brand_preference format: {lead_data.brand_preference}")
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid brand preference ID format: {lead_data.brand_preference}"
                        )
                else:
                    update_data["brand_preference"] = None

            # Budget preference with validation
            if lead_data.budget_preference is not None:
                if lead_data.budget_preference is not None and lead_data.budget_preference < 0:
                    raise HTTPException(
                        status_code=400,
                        detail="Budget preference cannot be negative"
                    )
                update_data["budget_preference"] = lead_data.budget_preference

            # Franchise buyer information fields
            if lead_data.franchise_interested_in is not None:
                update_data["franchise_interested_in"] = lead_data.franchise_interested_in.strip() if lead_data.franchise_interested_in else None
            if lead_data.looking_for_business_opportunity_since is not None:
                update_data["looking_for_business_opportunity_since"] = lead_data.looking_for_business_opportunity_since.strip() if lead_data.looking_for_business_opportunity_since else None
            if lead_data.skills is not None:
                update_data["skills"] = lead_data.skills.strip() if lead_data.skills else None
            if lead_data.looking_to_be_owner_operator is not None:
                update_data["looking_to_be_owner_operator"] = lead_data.looking_to_be_owner_operator.strip() if lead_data.looking_to_be_owner_operator else None
            if lead_data.when_looking_to_start is not None:
                update_data["when_looking_to_start"] = lead_data.when_looking_to_start.strip() if lead_data.when_looking_to_start else None
            if lead_data.ethnic_background is not None:
                update_data["ethnic_background"] = lead_data.ethnic_background.strip() if lead_data.ethnic_background else None
            if lead_data.funds_to_invest is not None:
                update_data["funds_to_invest"] = lead_data.funds_to_invest.strip() if lead_data.funds_to_invest else None
            if lead_data.eoi_nda_link is not None:
                update_data["eoi_nda_link"] = lead_data.eoi_nda_link.strip() if lead_data.eoi_nda_link else None
            if lead_data.work_background is not None:
                update_data["work_background"] = lead_data.work_background.strip() if lead_data.work_background else None
            if lead_data.motivation_to_enquire is not None:
                update_data["motivation_to_enquire"] = lead_data.motivation_to_enquire.strip() if lead_data.motivation_to_enquire else None
            if lead_data.funds_available is not None:
                update_data["funds_available"] = lead_data.funds_available.strip() if lead_data.funds_available else None
            if lead_data.motivation is not None:
                update_data["motivation"] = lead_data.motivation.strip() if lead_data.motivation else None
            if lead_data.have_run_business_before is not None:
                update_data["have_run_business_before"] = lead_data.have_run_business_before
            if lead_data.have_mortgage is not None:
                update_data["have_mortgage"] = lead_data.have_mortgage
            if lead_data.high_net_worth is not None:
                update_data["high_net_worth"] = lead_data.high_net_worth.strip() if lead_data.high_net_worth else None

            # Status fields
            if lead_data.is_active is not None:
                update_data["is_active"] = lead_data.is_active

            # Perform update if there are changes
            if update_data:
                logger.info(f"Updating lead {lead_id} with data: {list(update_data.keys())}")
                await self.db.execute(
                    update(Lead)
                    .where(Lead.id == lead_uuid)
                    .values(**update_data)
                )
                await self.db.commit()
                logger.info(f"Successfully updated lead {lead_id}")
            else:
                logger.info(f"No changes to update for lead {lead_id}")

            # Return updated lead with related data
            return await self.get_lead_by_id(lead_id, user_role, user_id)

        except HTTPException:
            # Re-raise HTTPExceptions (they already have proper error messages)
            await self.db.rollback()
            raise
        except ValueError as e:
            await self.db.rollback()
            logger.error(f"Validation error in update_lead: {e}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid data format: {str(e)}"
            )
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Database integrity error updating lead {lead_id}: {e}")
            if "duplicate key" in str(e).lower():
                if "email" in str(e).lower():
                    raise HTTPException(
                        status_code=409,
                        detail="A lead with this email address already exists"
                    )
                elif "phone" in str(e).lower():
                    raise HTTPException(
                        status_code=409,
                        detail="A lead with this phone number already exists"
                    )
                else:
                    raise HTTPException(
                        status_code=409,
                        detail="Update conflicts with existing data"
                    )
            else:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid data provided for update"
                )
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error updating lead {lead_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="Database error occurred while updating lead"
            )
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error updating lead {lead_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while updating the lead"
            )
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error updating lead: {e}")
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while updating the lead"
            )
    
    async def delete_lead(self, lead_id: str, user_role: str = None, user_id: str = None) -> bool:
        """Soft delete a lead with permission checking"""
        try:
            lead = await self.get_lead_by_id(lead_id, user_role, user_id)
            if not lead:
                return False

            # Check if already deleted
            if lead.is_deleted:
                logger.warning(f"Lead {lead_id} is already deleted")
                return True

            # Convert string ID to UUID for queries
            lead_uuid = uuid.UUID(lead_id)

            # Soft delete the lead by setting is_deleted = True and is_active = False
            await self.db.execute(
                update(Lead)
                .where(Lead.id == lead_uuid)
                .values(
                    is_deleted=True,
                    is_active=False,
                    updated_at=func.now()
                )
            )

            await self.db.commit()
            logger.info(f"Successfully soft deleted lead {lead_id}")
            return True
        except ValueError as e:
            await self.db.rollback()
            logger.error(f"Invalid lead ID in delete_lead: {e}")
            raise HTTPException(
                status_code=400,
                detail="Invalid lead ID format"
            )
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error deleting lead: {e}")
            raise HTTPException(
                status_code=500,
                detail="Database error occurred while deleting lead"
            )
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error deleting lead: {e}")
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while deleting the lead"
            )
    
    async def search_leads(
        self,
        query: str,
        skip: int = 0,
        limit: int = 20
    ) -> List[LeadResponse]:
        """Search leads by query"""
        try:
            search_query = select(Lead).options(
                selectinload(Lead.lead_source_rel),
                selectinload(Lead.lead_status_rel),
                selectinload(Lead.franchisor)
            ).where(
                or_(
                    Lead.first_name.ilike(f"%{query}%"),
                    Lead.last_name.ilike(f"%{query}%"),
                    Lead.email.ilike(f"%{query}%"),
                    Lead.phone.ilike(f"%{query}%"),
                    Lead.mobile.ilike(f"%{query}%"),
                    Lead.location.ilike(f"%{query}%")
                )
            ).offset(skip).limit(limit).order_by(desc(Lead.created_at))
            
            result = await self.db.execute(search_query)
            leads = result.scalars().all()
            
            # Convert to response models using helper method
            lead_responses = []
            for lead in leads:
                lead_response = await self._lead_to_response(lead)
                lead_responses.append(lead_response)
            
            return lead_responses
        except ValueError as e:
            logger.error(f"Invalid search parameters: {e}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid search parameters: {str(e)}"
            )
        except SQLAlchemyError as e:
            logger.error(f"Database error searching leads: {e}")
            raise HTTPException(
                status_code=500,
                detail="Database error occurred while searching leads"
            )
        except Exception as e:
            logger.error(f"Unexpected error searching leads: {e}")
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while searching leads"
            )
    
    async def activate_lead(self, lead_id: str) -> Optional[LeadResponse]:
        """Activate a lead by setting is_active=True"""
        try:
            lead = await self.get_lead_by_id(lead_id)
            if not lead:
                return None
            
            if lead.is_deleted:
                logger.warning(f"Cannot activate deleted lead {lead_id}")
                return None
            
            # Update the lead to activate it
            await self.db.execute(
                update(Lead)
                .where(Lead.id == uuid.UUID(lead_id))
                .values(is_active=True, updated_at=func.now())
            )
            await self.db.commit()
            
            # Get the updated lead
            updated_lead = await self.get_lead_by_id(lead_id)
            if updated_lead:
                return LeadResponse(
                    id=str(updated_lead.id),
                    zoho_lead_id=updated_lead.zoho_lead_id,
                    full_name=updated_lead.full_name,
                    phone=updated_lead.phone,
                    mobile=updated_lead.mobile,
                    email=updated_lead.email,
                    location=updated_lead.location,
                    lead_source=updated_lead.lead_source,
                    brand_preference=str(updated_lead.brand_preference) if updated_lead.brand_preference else None,
                    budget_preference=updated_lead.budget_preference,
                    qualification_status=LeadStatus(updated_lead.qualification_status),
                    status=updated_lead.status,
                    is_active=updated_lead.is_active,
                    is_deleted=updated_lead.is_deleted,
                    created_at=updated_lead.created_at,
                    updated_at=updated_lead.updated_at
                )
            return None
            
        except ValueError as e:
            await self.db.rollback()
            logger.error(f"Invalid lead ID in activate_lead: {e}")
            raise HTTPException(
                status_code=400,
                detail="Invalid lead ID format"
            )
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error activating lead {lead_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="Database error occurred while activating lead"
            )
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error activating lead {lead_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while activating the lead"
            )

    async def deactivate_lead(self, lead_id: str) -> Optional[LeadResponse]:
        """Deactivate a lead by setting is_active=False"""
        try:
            lead = await self.get_lead_by_id(lead_id)
            if not lead:
                return None
            
            if lead.is_deleted:
                logger.warning(f"Cannot deactivate deleted lead {lead_id}")
                return None
            
            # Update the lead to deactivate it
            await self.db.execute(
                update(Lead)
                .where(Lead.id == uuid.UUID(lead_id))
                .values(is_active=False, updated_at=func.now())
            )
            await self.db.commit()
            
            # Get the updated lead
            updated_lead = await self.get_lead_by_id(lead_id)
            if updated_lead:
                return LeadResponse(
                    id=str(updated_lead.id),
                    zoho_lead_id=updated_lead.zoho_lead_id,
                    full_name=updated_lead.full_name,
                    phone=updated_lead.phone,
                    mobile=updated_lead.mobile,
                    email=updated_lead.email,
                    location=updated_lead.location,
                    lead_source=updated_lead.lead_source,
                    brand_preference=str(updated_lead.brand_preference) if updated_lead.brand_preference else None,
                    budget_preference=updated_lead.budget_preference,
                    qualification_status=LeadStatus(updated_lead.qualification_status),
                    status=updated_lead.status,
                    is_active=updated_lead.is_active,
                    is_deleted=updated_lead.is_deleted,
                    created_at=updated_lead.created_at,
                    updated_at=updated_lead.updated_at
                )
            return None
            
        except ValueError as e:
            await self.db.rollback()
            logger.error(f"Invalid lead ID in deactivate_lead: {e}")
            raise HTTPException(
                status_code=400,
                detail="Invalid lead ID format"
            )
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error deactivating lead {lead_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="Database error occurred while deactivating lead"
            )
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error deactivating lead {lead_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while deactivating the lead"
            )
    
    async def bulk_upload_leads(self, file: UploadFile, created_by: str = None) -> Dict[str, Any]:
        """Bulk upload leads from CSV file with duplicate detection"""
        try:
            # Validate file type
            if not file.filename or not file.filename.endswith('.csv'):
                logger.error(f"Invalid file type: {file.filename}")
                raise HTTPException(status_code=400, detail="File must be a CSV file")

            # Read file content
            content = await file.read()
            csv_content = content.decode('utf-8')
            logger.info(f"CSV content preview: {csv_content[:200]}...")

            # Parse CSV
            csv_reader = csv.DictReader(io.StringIO(csv_content))

            # Required headers (only full_name is truly required now)
            required_headers = {'full_name'}

            # All expected headers (including optional ones)
            expected_headers = {
                'full_name', 'phone', 'mobile', 'email', 'location',
                'lead_source', 'brand_preference', 'budget_preference',
                'qualification_status', 'status', 'is_deleted'
            }

            # Log the actual headers found
            logger.info(f"CSV headers found: {csv_reader.fieldnames}")
            logger.info(f"Required headers: {required_headers}")

            # Validate required headers
            if not required_headers.issubset(set(csv_reader.fieldnames or [])):
                missing_headers = required_headers - set(csv_reader.fieldnames or [])
                error_msg = f"Missing required headers: {', '.join(missing_headers)}"
                logger.error(error_msg)
                raise HTTPException(
                    status_code=422,
                    detail=error_msg
                )

            leads_to_create = []
            duplicates = []
            errors = []
            processed_contacts = set()  # For tracking phone and mobile numbers
            processed_emails = set()

            # Check existing leads for duplicate detection
            existing_phones_query = select(Lead.phone).where(Lead.phone.isnot(None))
            existing_mobiles_query = select(Lead.mobile).where(Lead.mobile.isnot(None))
            existing_emails_query = select(Lead.email).where(Lead.email.isnot(None))

            existing_phones_result = await self.db.execute(existing_phones_query)
            existing_mobiles_result = await self.db.execute(existing_mobiles_query)
            existing_emails_result = await self.db.execute(existing_emails_query)

            existing_phones = {row[0] for row in existing_phones_result.fetchall()}
            existing_mobiles = {row[0] for row in existing_mobiles_result.fetchall()}
            existing_emails = {row[0] for row in existing_emails_result.fetchall()}

            row_number = 1
            for row in csv_reader:
                row_number += 1
                try:
                    # Trim whitespace from all fields
                    row = {k: v.strip() if v else None for k, v in row.items()}

                    # Validate required fields
                    if not row.get('full_name'):
                        errors.append(f"Row {row_number}: Missing required field (full_name)")
                        continue

                    # Check for duplicates within file and database
                    phone = row.get('phone')
                    mobile = row.get('mobile')
                    email = row.get('email')

                    # Check phone duplicates
                    if phone and (phone in processed_contacts or phone in existing_phones):
                        duplicates.append(f"Row {row_number}: Duplicate phone number {phone}")
                        continue

                    # Check mobile duplicates
                    if mobile and (mobile in processed_contacts or mobile in existing_mobiles):
                        duplicates.append(f"Row {row_number}: Duplicate mobile number {mobile}")
                        continue

                    # Check email duplicates
                    if email and (email in processed_emails or email in existing_emails):
                        duplicates.append(f"Row {row_number}: Duplicate email {email}")
                        continue

                    # Convert budget_preference to Decimal
                    budget_preference = None
                    if row.get('budget_preference'):
                        try:
                            budget_preference = Decimal(str(row['budget_preference']))
                        except (InvalidOperation, ValueError):
                            errors.append(f"Row {row_number}: Invalid budget_preference value")
                            continue

                    # Convert boolean fields
                    is_deleted = False  # Default value
                    if row.get('is_deleted'):
                        is_deleted_str = str(row['is_deleted']).lower().strip()
                        if is_deleted_str in ['true', '1', 'yes', 'y']:
                            is_deleted = True
                        elif is_deleted_str in ['false', '0', 'no', 'n']:
                            is_deleted = False
                        else:
                            errors.append(f"Row {row_number}: Invalid is_deleted value (use true/false, 1/0, yes/no)")
                            continue

                    # Convert brand_preference to UUID if provided
                    brand_preference_uuid = None
                    if row.get('brand_preference'):
                        try:
                            brand_preference_uuid = uuid.UUID(row['brand_preference'])
                        except ValueError:
                            logger.warning(f"Row {row_number}: Invalid UUID format for brand_preference: {row['brand_preference']}")
                            brand_preference_uuid = None

                    # Create lead object
                    lead = Lead(
                        id=uuid.uuid4(),
                        full_name=row['full_name'],
                        phone=phone,
                        mobile=mobile,
                        email=email,
                        location=row.get('location'),
                        lead_source=row.get('lead_source'),
                        brand_preference=brand_preference_uuid,
                        budget_preference=budget_preference,
                        qualification_status=row.get('qualification_status', 'new'),
                        status=row.get('status', 'new'),
                        is_active=row.get('is_active', True),
                        is_deleted=is_deleted
                    )

                    leads_to_create.append(lead)
                    # Track processed contacts (phone and mobile)
                    if phone:
                        processed_contacts.add(phone)
                    if mobile:
                        processed_contacts.add(mobile)
                    if email:
                        processed_emails.add(email)

                except Exception as e:
                    errors.append(f"Row {row_number}: {str(e)}")
                    continue

            # Bulk save leads
            if leads_to_create:
                self.db.add_all(leads_to_create)
                await self.db.commit()

                # Refresh all leads to get the created_at timestamps
                for lead in leads_to_create:
                    await self.db.refresh(lead)

            result = {
                "total_processed": row_number - 1,
                "successful_imports": len(leads_to_create),
                "duplicates_found": len(duplicates),
                "errors_found": len(errors),
                "duplicates": duplicates,
                "errors": errors
            }

            logger.info(f"Bulk upload completed: {result}")
            return result

        except HTTPException:
            raise
        except ValueError as e:
            logger.error(f"Invalid data in bulk upload: {e}")
            await self.db.rollback()
            raise HTTPException(
                status_code=400,
                detail=f"Invalid data format in CSV file: {str(e)}"
            )
        except IntegrityError as e:
            logger.error(f"Database integrity error in bulk upload: {e}")
            await self.db.rollback()
            raise HTTPException(
                status_code=409,
                detail="Duplicate data found in CSV file or database"
            )
        except SQLAlchemyError as e:
            logger.error(f"Database error in bulk upload: {e}")
            await self.db.rollback()
            raise HTTPException(
                status_code=500,
                detail="Database error occurred during bulk upload"
            )
        except Exception as e:
            logger.error(f"Unexpected error in bulk upload: {e}")
            await self.db.rollback()
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred during bulk upload"
            )

    async def get_lead_communications(self, lead_id: str) -> List[Dict[str, Any]]:
        """Get communication history for a lead"""
        try:
            # Validate lead exists
            lead = await self.get_lead_by_id(lead_id)
            if not lead:
                raise HTTPException(status_code=404, detail="Lead not found")

            # Query communications
            query = select(Communication).where(Communication.lead_id == uuid.UUID(lead_id)).order_by(desc(Communication.created_at))
            result = await self.db.execute(query)
            communications = result.scalars().all()

            # Convert to response format
            communication_list = []
            for comm in communications:
                communication_list.append({
                    "id": str(comm.id),
                    "communication_type": comm.communication_type,
                    "subject": comm.subject,
                    "content": comm.content,
                    "direction": comm.direction,
                    "user_id": str(comm.user_id) if comm.user_id else None,
                    "created_at": comm.created_at,
                    "updated_at": comm.updated_at
                })

            return communication_list

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting lead communications: {e}")
            raise HTTPException(status_code=500, detail="Failed to retrieve communication history")
