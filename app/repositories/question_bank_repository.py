"""
Question Bank Repository
Database operations for question bank management
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, func, text
from sqlalchemy.orm import selectinload
from app.models.lead import Question
from app.models.franchisor import Franchisor
from app.schemas.question_bank import QuestionCreateRequest, QuestionUpdateRequest
import logging
import uuid

logger = logging.getLogger(__name__)


class QuestionBankRepository:
    """Repository for question bank operations."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_franchisors_for_dropdown(self) -> List[Dict[str, Any]]:
        """
        Get franchisors for dropdown selection.

        Returns:
            List of franchisors with id, name, and industry
        """
        try:
            # Import Industry model
            from app.models.industry import Industry

            query = select(
                Franchisor.id,
                Franchisor.name,
                Industry.name.label("industry_name")
            ).outerjoin(
                Industry, Franchisor.industry_id == Industry.id
            ).where(
                Franchisor.is_deleted == False
            ).order_by(Franchisor.name)

            result = await self.db.execute(query)
            franchisors = result.fetchall()

            return [
                {
                    "id": franchisor.id,
                    "name": franchisor.name,
                    "industry": franchisor.industry_name if franchisor.industry_name else "Uncategorized"
                }
                for franchisor in franchisors
            ]
        except Exception as e:
            logger.error(f"Error fetching franchisors for dropdown: {e}")
            raise

    async def get_max_order_for_franchisor(self, franchisor_id: uuid.UUID) -> int:
        """
        Get the maximum order for a franchisor's questions.
        
        Args:
            franchisor_id: The franchisor ID
            
        Returns:
            Maximum order number (0 if no questions exist)
        """
        try:
            query = select(func.coalesce(func.max(Question.order_sequence), 0)).where(
                and_(
                    Question.franchisor_id == franchisor_id,
                    Question.is_deleted == False
                )
            )
            
            result = await self.db.execute(query)
            max_order = result.scalar()
            
            return max_order or 0
        except Exception as e:
            logger.error(f"Error getting max order for franchisor {franchisor_id}: {e}")
            raise

    async def create_question(self, question_data: QuestionCreateRequest) -> Question:
        """
        Create a new question with auto-calculated order.
        
        Args:
            question_data: Question creation data
            
        Returns:
            Created question object
        """
        try:
            logger.info("Starting question creation process")
            # Get the maximum order for this franchisor
            max_order = await self.get_max_order_for_franchisor(question_data.franchisor_id)
            logger.info(f"Retrieved max order for franchisor: {max_order}")
            
            # If no existing questions found, start with order 1, otherwise increment
            next_order = 1 if max_order == 0 else max_order + 1
            logger.info(f"Calculated next order: {next_order}")
            
            # Create question object
            question = Question(
                franchisor_id=question_data.franchisor_id,
                question_text=question_data.question_text,
                question_internal_text=question_data.question_text,  # Store original text
                question_type=question_data.question_type,
                expected_answer=question_data.expected_answer,  # Store expected answers as JSONB
                order_sequence=next_order,
                is_required=question_data.is_required,
                is_deleted=False,
                is_active=True,

                # New fields for pre-qualification questions
                question_id=question_data.question_id,
                category=question_data.category,
                score_weight=question_data.score_weight,
                context_info=question_data.context_info,
                qualification_weight=question_data.qualification_weight,
                expected_answer_type=question_data.expected_answer_type,
                answer_options=question_data.answer_options,
                passing_criteria=question_data.passing_criteria,
                validation_rules=question_data.validation_rules,
                requires_follow_up=question_data.requires_follow_up,
                follow_up_logic=question_data.follow_up_logic
            )
            logger.info(f"Created question object: {question.id}")
            
            self.db.add(question)
            await self.db.commit()
            await self.db.refresh(question)
            logger.info(f"Successfully saved question to database: {question.id}")
            
            return question
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating question: {e}")
            raise

    async def get_question_by_id(self, question_id: uuid.UUID) -> Optional[Question]:
        """
        Get a question by ID with franchisor details.
        
        Args:
            question_id: Question ID
            
        Returns:
            Question object with franchisor details or None
        """
        try:
            query = select(Question).options(
                selectinload(Question.franchisor).selectinload(Franchisor.industry_rel)
            ).where(
                and_(
                    Question.id == question_id,
                    Question.is_deleted == False
                )
            )
            
            result = await self.db.execute(query)
            question = result.scalar_one_or_none()
            
            return question
        except Exception as e:
            logger.error(f"Error fetching question {question_id}: {e}")
            raise

    async def update_question(self, question_id: uuid.UUID, question_data: QuestionUpdateRequest) -> Optional[Question]:
        """
        Update a question (excluding order and question_internal_text).
        
        Args:
            question_id: Question ID
            question_data: Updated question data
            
        Returns:
            Updated question object or None
        """
        try:
            # Get existing question
            question = await self.get_question_by_id(question_id)
            if not question:
                return None
            
            # Update allowed fields
            question.question_text = question_data.question_text
            question.question_type = question_data.question_type
            question.expected_answer = question_data.expected_answer  # Update expected answers

            # Update new fields for pre-qualification questions
            if question_data.question_id is not None:
                question.question_id = question_data.question_id
            if question_data.category is not None:
                question.category = question_data.category
            if question_data.score_weight is not None:
                question.score_weight = question_data.score_weight
            if question_data.context_info is not None:
                question.context_info = question_data.context_info
            if question_data.qualification_weight is not None:
                question.qualification_weight = question_data.qualification_weight
            if question_data.expected_answer_type is not None:
                question.expected_answer_type = question_data.expected_answer_type
            if question_data.answer_options is not None:
                question.answer_options = question_data.answer_options
            if question_data.passing_criteria is not None:
                question.passing_criteria = question_data.passing_criteria
            if question_data.validation_rules is not None:
                question.validation_rules = question_data.validation_rules
            if question_data.requires_follow_up is not None:
                question.requires_follow_up = question_data.requires_follow_up
            if question_data.follow_up_logic is not None:
                question.follow_up_logic = question_data.follow_up_logic
            if question_data.is_required is not None:
                question.is_required = question_data.is_required
            
            await self.db.commit()
            await self.db.refresh(question)
            
            return question
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating question {question_id}: {e}")
            raise

    async def soft_delete_question(self, question_id: uuid.UUID) -> bool:
        """
        Soft delete a question and reorder subsequent questions.

        When a question is deleted, all questions with higher order_sequence
        in the same franchisor will have their order decremented by 1.

        Args:
            question_id: Question ID to delete

        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            # First, get the question to be deleted to know its order and franchisor
            question_query = select(Question).where(
                and_(
                    Question.id == question_id,
                    Question.is_deleted == False
                )
            )

            result = await self.db.execute(question_query)
            question_to_delete = result.scalar_one_or_none()

            if not question_to_delete:
                return False

            deleted_order = question_to_delete.order_sequence
            franchisor_id = question_to_delete.franchisor_id

            # Delete the question
            delete_query = update(Question).where(
                and_(
                    Question.id == question_id,
                    Question.is_deleted == False
                )
            ).values(is_deleted=True)

            delete_result = await self.db.execute(delete_query)

            if delete_result.rowcount == 0:
                return False

            # Update order sequence for all subsequent questions in the same franchisor
            # Decrement order_sequence by 1 for all questions with order > deleted_order
            reorder_query = update(Question).where(
                and_(
                    Question.franchisor_id == franchisor_id,
                    Question.order_sequence > deleted_order,
                    Question.is_deleted == False
                )
            ).values(order_sequence=Question.order_sequence - 1)

            await self.db.execute(reorder_query)
            await self.db.commit()

            logger.info(f"Successfully deleted question {question_id} with order {deleted_order} and reordered subsequent questions")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error soft deleting question {question_id}: {e}")
            raise

    async def toggle_question_status(self, question_id: uuid.UUID) -> Optional[Question]:
        """
        Toggle question status between active and inactive.
        
        Args:
            question_id: Question ID to toggle
            
        Returns:
            Updated question object or None
        """
        try:
            question = await self.get_question_by_id(question_id)
            if not question:
                return None
            
            # Toggle status
            question.is_active = not question.is_active
            
            await self.db.commit()
            await self.db.refresh(question)
            
            return question
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error toggling question status {question_id}: {e}")
            raise

    async def get_questions_by_franchisor(self, franchisor_id: uuid.UUID) -> List[Dict[str, Any]]:
        """
        Get all questions for a specific franchisor.

        Args:
            franchisor_id: The franchisor ID

        Returns:
            List of questions with franchisor details
        """
        try:
            # Import Industry model
            from app.models.industry import Industry

            query = select(Question).options(
                selectinload(Question.franchisor).selectinload(Franchisor.industry_rel)
            ).where(
                and_(
                    Question.franchisor_id == franchisor_id,
                    Question.is_deleted == False
                )
            ).order_by(Question.order_sequence)

            result = await self.db.execute(query)
            questions = result.scalars().all()

            return [
                {
                    "id": q.id,
                    "franchisor_id": q.franchisor_id,
                    "franchisor_name": q.franchisor.name if q.franchisor else None,
                    "franchisor_category": q.franchisor.industry_rel.name if q.franchisor and q.franchisor.industry_rel else "Uncategorized",
                    "question_text": q.question_text,
                    "question_internal_text": q.question_internal_text,
                    "question_type": q.question_type,
                    "expected_answer": q.expected_answer if q.expected_answer else None,
                    "order_sequence": q.order_sequence,
                    "is_active": q.is_active,
                    "created_at": q.created_at,
                    "updated_at": q.updated_at,

                    # New fields for pre-qualification questions
                    "question_id": q.question_id,
                    "category": q.category,
                    "score_weight": q.score_weight,
                    "context_info": q.context_info,
                    "qualification_weight": q.qualification_weight,
                    "expected_answer_type": q.expected_answer_type,
                    "answer_options": q.answer_options,
                    "passing_criteria": q.passing_criteria,
                    "validation_rules": q.validation_rules,
                    "requires_follow_up": q.requires_follow_up,
                    "follow_up_logic": q.follow_up_logic,
                    "is_required": q.is_required,
                    "is_deleted": q.is_deleted,
                    "deleted_at": q.deleted_at
                }
                for q in questions
            ]
        except Exception as e:
            logger.error(f"Error fetching questions for franchisor {franchisor_id}: {e}")
            raise
    async def get_all_questions_paginated(
        self,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        franchisor_id: Optional[uuid.UUID] = None,
        is_active: Optional[bool] = None,
        sort_by: Optional[str] = "created_at",
        sort_order: Optional[str] = "desc"
    ) -> Dict[str, Any]:
        """
        Get all questions with pagination, filtering, and sorting.

        Args:
            page: Page number
            size: Page size
            search: Search term for question text
            franchisor_id: Filter by franchisor ID
            is_active: Filter by active status
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)

        Returns:
            Dictionary with questions and pagination info
        """
        try:
            # Build base query
            base_query = select(Question).options(
                selectinload(Question.franchisor).selectinload(Franchisor.industry_rel)
            ).where(Question.is_deleted == False)
            
            # Apply filters
            if search:
                base_query = base_query.where(
                    Question.question_text.ilike(f"%{search}%")
                )
            
            if franchisor_id:
                base_query = base_query.where(Question.franchisor_id == franchisor_id)
            
            if is_active is not None:
                base_query = base_query.where(Question.is_active == is_active)
            
            # Count total
            count_query = select(func.count()).select_from(base_query.subquery())
            total_result = await self.db.execute(count_query)
            total = total_result.scalar()
            
            # Apply sorting
            sort_column = Question.created_at  # default
            if sort_by == "question_text":
                sort_column = Question.question_text
            elif sort_by == "order_sequence":
                sort_column = Question.order_sequence
            elif sort_by == "created_at":
                sort_column = Question.created_at

            if sort_order == "asc":
                base_query = base_query.order_by(sort_column.asc())
            else:
                base_query = base_query.order_by(sort_column.desc())

            # Get paginated results
            offset = (page - 1) * size
            query = base_query.offset(offset).limit(size)
            
            result = await self.db.execute(query)
            questions = result.scalars().all()
            
            # Calculate pagination info
            pages = (total + size - 1) // size
            
            return {
                "items": [
                    {
                        "id": q.id,
                        "franchisor_id": q.franchisor_id,
                        "franchisor_name": q.franchisor.name if q.franchisor else None,
                        "franchisor_category": q.franchisor.industry_rel.name if q.franchisor and q.franchisor.industry_rel else "Uncategorized",
                        "question_text": q.question_text,
                        "question_type": q.question_type,
                        "expected_answer": q.expected_answer if q.expected_answer else None,
                        "order_sequence": q.order_sequence,
                        "is_active": q.is_active,
                        "created_at": q.created_at,

                        # New fields for pre-qualification questions
                        "question_id": q.question_id,
                        "category": q.category,
                        "score_weight": q.score_weight,
                        "context_info": q.context_info,
                        "qualification_weight": q.qualification_weight,
                        "expected_answer_type": q.expected_answer_type,
                        "answer_options": q.answer_options,
                        "passing_criteria": q.passing_criteria,
                        "validation_rules": q.validation_rules,
                        "requires_follow_up": q.requires_follow_up,
                        "follow_up_logic": q.follow_up_logic,
                        "is_required": q.is_required,
                        "is_deleted": q.is_deleted,
                        "deleted_at": q.deleted_at
                    }
                    for q in questions
                ],
                "total": total,
                "page": page,
                "size": size,
                "pages": pages
            }
        except Exception as e:
            logger.error(f"Error fetching paginated questions: {e}")
            raise

    async def reorder_questions(self, franchisor_id: uuid.UUID, question_orders: List[Dict[str, Any]]) -> bool:
        """
        Reorder questions for a specific franchisor.
        
        Args:
            franchisor_id: Franchisor ID
            question_orders: List of dicts with question_id and new order
            
        Returns:
            True if reordered successfully
        """
        try:
            # Verify all questions belong to the franchisor
            question_ids = [item["question_id"] for item in question_orders]
            verify_query = select(Question.id).where(
                and_(
                    Question.id.in_(question_ids),
                    Question.franchisor_id == franchisor_id,
                    Question.is_deleted == False
                )
            )
            
            result = await self.db.execute(verify_query)
            existing_ids = {row[0] for row in result.fetchall()}
            
            if len(existing_ids) != len(question_ids):
                raise ValueError("Some questions do not belong to the specified franchisor")
            
            # Update orders in a single transaction
            for item in question_orders:
                update_query = update(Question).where(
                    and_(
                        Question.id == item["question_id"],
                        Question.franchisor_id == franchisor_id
                    )
                ).values(order_sequence=item["order"])
                
                await self.db.execute(update_query)
            
            await self.db.commit()
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error reordering questions for franchisor {franchisor_id}: {e}")
            raise

    async def validate_order_sequence_integrity(self, franchisor_id: uuid.UUID) -> Dict[str, Any]:
        """
        Validate that order sequences for a franchisor are sequential without gaps or duplicates.

        Args:
            franchisor_id: Franchisor ID to validate

        Returns:
            Dictionary with validation results
        """
        try:
            # Get all questions for the franchisor ordered by sequence
            query = select(Question.id, Question.order_sequence).where(
                and_(
                    Question.franchisor_id == franchisor_id,
                    Question.is_deleted == False
                )
            ).order_by(Question.order_sequence)

            result = await self.db.execute(query)
            questions = result.fetchall()

            if not questions:
                return {
                    "is_valid": True,
                    "total_questions": 0,
                    "issues": []
                }

            orders = [q.order_sequence for q in questions]
            total_questions = len(orders)
            expected_orders = list(range(1, total_questions + 1))
            issues = []

            # Check for duplicates
            if len(orders) != len(set(orders)):
                duplicates = [order for order in set(orders) if orders.count(order) > 1]
                issues.append(f"Duplicate order numbers found: {duplicates}")

            # Check for gaps or wrong sequence
            if orders != expected_orders:
                missing = set(expected_orders) - set(orders)
                extra = set(orders) - set(expected_orders)

                if missing:
                    issues.append(f"Missing order numbers: {sorted(missing)}")
                if extra:
                    issues.append(f"Invalid order numbers: {sorted(extra)}")

            return {
                "is_valid": len(issues) == 0,
                "total_questions": total_questions,
                "current_orders": orders,
                "expected_orders": expected_orders,
                "issues": issues
            }

        except Exception as e:
            logger.error(f"Error validating order sequence for franchisor {franchisor_id}: {e}")
            raise

    async def fix_order_sequence_gaps(self, franchisor_id: uuid.UUID) -> bool:
        """
        Fix order sequence gaps by reassigning sequential order numbers.

        Args:
            franchisor_id: Franchisor ID to fix

        Returns:
            True if fixed successfully
        """
        try:
            # Get all questions ordered by current sequence
            query = select(Question).where(
                and_(
                    Question.franchisor_id == franchisor_id,
                    Question.is_deleted == False
                )
            ).order_by(Question.order_sequence, Question.created_at)

            result = await self.db.execute(query)
            questions = result.scalars().all()

            if not questions:
                return True

            # Reassign sequential order numbers
            for index, question in enumerate(questions, start=1):
                if question.order_sequence != index:
                    question.order_sequence = index

            await self.db.commit()
            logger.info(f"Fixed order sequence for {len(questions)} questions in franchisor {franchisor_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error fixing order sequence for franchisor {franchisor_id}: {e}")
            raise
