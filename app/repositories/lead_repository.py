"""
Lead Repository - Data Access Layer
Production-grade repository implementation following established patterns
"""

import uuid
from typing import Optional, <PERSON>, Tuple, Union
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal

from app.repositories.base import BaseRepository
from app.models.lead import Lead, Communication
from app.schemas.lead import LeadCreateRequest, LeadUpdateRequest, LeadStatus
from app.core.logging import logger


class LeadRepository(BaseRepository[Lead, LeadCreateRequest, LeadUpdateRequest]):
    """Repository for Lead data access operations"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Lead, db)
    
    async def get_by_id(self, lead_id: Union[str, uuid.UUID], include_deleted: bool = False) -> Optional[Lead]:
        """Get lead by ID"""
        try:
            query = select(Lead).where(Lead.id == lead_id)
            
            if not include_deleted:
                query = query.where(Lead.is_deleted == False)
            
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting lead by ID {lead_id}: {e}")
            raise
    
    async def get_by_contact_info(self, contact_number: str, email: Optional[str] = None) -> Optional[Lead]:
        """Get lead by contact number or email for duplicate detection"""
        try:
            filters = [Lead.is_deleted == False]
            
            if email:
                filters.append(
                    or_(
                        Lead.contact_number == contact_number,
                        Lead.email == email
                    )
                )
            else:
                filters.append(Lead.contact_number == contact_number)
            
            query = select(Lead).where(and_(*filters))
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting lead by contact info: {e}")
            raise
    
    async def get_multi_with_filters(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        status: Optional[LeadStatus] = None,
        is_active: Optional[bool] = None,
        lead_source: Optional[str] = None
    ) -> Tuple[List[Lead], int]:
        """Get leads with filtering and pagination"""
        try:
            # Base query
            query = select(Lead).where(Lead.is_deleted == False)
            
            # Apply filters
            if search:
                search_filter = or_(
                    Lead.full_name.ilike(f"%{search}%"),
                    Lead.email.ilike(f"%{search}%"),
                    Lead.contact_number.ilike(f"%{search}%"),
                    Lead.location.ilike(f"%{search}%")
                )
                query = query.where(search_filter)
            
            if status:
                query = query.where(Lead.qualification_status == status.value)
            
            if is_active is not None:
                query = query.where(Lead.is_active == is_active)
            
            if lead_source:
                query = query.where(Lead.lead_source.ilike(f"%{lead_source}%"))
            
            # Get total count
            count_query = select(func.count(Lead.id)).where(Lead.is_deleted == False)
            if search:
                count_query = count_query.where(search_filter)
            if status:
                count_query = count_query.where(Lead.qualification_status == status.value)
            if is_active is not None:
                count_query = count_query.where(Lead.is_active == is_active)
            if lead_source:
                count_query = count_query.where(Lead.lead_source.ilike(f"%{lead_source}%"))
            
            total_count_result = await self.db.execute(count_query)
            total_count = total_count_result.scalar()
            
            # Apply pagination and ordering
            query = query.order_by(desc(Lead.created_at)).offset(skip).limit(limit)
            
            result = await self.db.execute(query)
            leads = list(result.scalars().all())
            
            return leads, total_count
            
        except Exception as e:
            logger.error(f"Error getting leads with filters: {e}")
            raise
    
    async def create_bulk(self, leads_data: List[LeadCreateRequest]) -> Tuple[List[Lead], List[str], List[str]]:
        """Create multiple leads with duplicate detection"""
        try:
            created_leads = []
            duplicates = []
            errors = []
            
            for lead_data in leads_data:
                try:
                    # Check for duplicates
                    existing_lead = await self.get_by_contact_info(
                        lead_data.contact_number, 
                        lead_data.email
                    )
                    
                    if existing_lead:
                        duplicates.append(f"Lead with contact {lead_data.contact_number} already exists")
                        continue
                    
                    # Create new lead
                    lead_dict = lead_data.model_dump()
                    lead_dict['id'] = uuid.uuid4()
                    
                    # Handle qualification status
                    if 'qualification_status' in lead_dict and lead_dict['qualification_status']:
                        if isinstance(lead_dict['qualification_status'], LeadStatus):
                            lead_dict['qualification_status'] = lead_dict['qualification_status'].value
                    else:
                        lead_dict['qualification_status'] = LeadStatus.NEW.value
                    
                    db_lead = Lead(**lead_dict)
                    self.db.add(db_lead)
                    created_leads.append(db_lead)
                    
                except Exception as e:
                    errors.append(f"Error creating lead {lead_data.full_name}: {str(e)}")
            
            if created_leads:
                await self.db.commit()
                for lead in created_leads:
                    await self.db.refresh(lead)
            
            return created_leads, duplicates, errors
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error in bulk lead creation: {e}")
            raise
    
    async def soft_delete(self, lead_id: Union[str, uuid.UUID]) -> bool:
        """Soft delete a lead"""
        try:
            lead = await self.get_by_id(lead_id)
            if lead:
                lead.is_deleted = True
                lead.is_active = False
                await self.db.commit()
                await self.db.refresh(lead)
                return True
            return False
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error soft deleting lead {lead_id}: {e}")
            raise
    
    async def activate(self, lead_id: Union[str, uuid.UUID]) -> bool:
        """Activate a lead"""
        try:
            lead = await self.get_by_id(lead_id)
            if lead and not lead.is_deleted:
                lead.is_active = True
                await self.db.commit()
                await self.db.refresh(lead)
                return True
            return False
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error activating lead {lead_id}: {e}")
            raise
    
    async def deactivate(self, lead_id: Union[str, uuid.UUID]) -> bool:
        """Deactivate a lead"""
        try:
            lead = await self.get_by_id(lead_id)
            if lead and not lead.is_deleted:
                lead.is_active = False
                await self.db.commit()
                await self.db.refresh(lead)
                return True
            return False
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error deactivating lead {lead_id}: {e}")
            raise
    
    async def update_status(self, lead_id: Union[str, uuid.UUID], status: LeadStatus) -> Optional[Lead]:
        """Update lead qualification status"""
        try:
            lead = await self.get_by_id(lead_id)
            if lead and not lead.is_deleted:
                lead.qualification_status = status.value
                await self.db.commit()
                await self.db.refresh(lead)
                return lead
            return None
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating lead status {lead_id}: {e}")
            raise
    
    async def get_communications(self, lead_id: Union[str, uuid.UUID]) -> List[Communication]:
        """Get all communications for a lead"""
        try:
            query = (
                select(Communication)
                .where(Communication.lead_id == lead_id)
                .order_by(desc(Communication.created_at))
            )
            result = await self.db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting communications for lead {lead_id}: {e}")
            raise
    
    async def get_lead_stats(self) -> dict:
        """Get lead statistics"""
        try:
            # Total leads
            total_query = select(func.count(Lead.id)).where(Lead.is_deleted == False)
            total_result = await self.db.execute(total_query)
            total_leads = total_result.scalar()
            
            # Active leads
            active_query = select(func.count(Lead.id)).where(
                and_(Lead.is_deleted == False, Lead.is_active == True)
            )
            active_result = await self.db.execute(active_query)
            active_leads = active_result.scalar()
            
            # Leads by status
            status_query = (
                select(Lead.qualification_status, func.count(Lead.id))
                .where(Lead.is_deleted == False)
                .group_by(Lead.qualification_status)
            )
            status_result = await self.db.execute(status_query)
            status_counts = dict(status_result.fetchall())
            
            return {
                "total_leads": total_leads,
                "active_leads": active_leads,
                "status_breakdown": status_counts
            }
        except Exception as e:
            logger.error(f"Error getting lead statistics: {e}")
            raise
