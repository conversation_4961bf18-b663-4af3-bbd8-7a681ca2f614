"""
Holiday Repository
Data access layer for holiday management
"""

from datetime import date, datetime, timezone
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy import select, and_, or_, func, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.holiday import Holiday
from app.schemas.holiday import HolidayCreateRequest, HolidayUpdateRequest
from app.repositories.base import BaseRepository
from app.core.logging import logger


class HolidayRepository(
    BaseRepository[Holiday, HolidayCreateRequest, HolidayUpdateRequest]
):
    """Repository for holiday management operations"""

    def __init__(self, db: AsyncSession):
        super().__init__(Holiday, db)

    async def get_all_holidays(
        self, skip: int = 0, limit: int = 100, include_inactive: bool = True
    ) -> List[Holiday]:
        """
        Get all holidays (active and optionally inactive, but not deleted)

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            include_inactive: Whether to include inactive holidays

        Returns:
            List of holidays
        """
        if include_inactive:
            query = (
                select(self.model)
                .where(self.model.is_deleted == False)
                .order_by(asc(self.model.date))
                .offset(skip)
                .limit(limit)
            )
        else:
            query = (
                select(self.model)
                .where(and_(self.model.is_active == True, self.model.is_deleted == False))
                .order_by(asc(self.model.date))
                .offset(skip)
                .limit(limit)
            )

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_active_holidays(
        self, skip: int = 0, limit: int = 100
    ) -> List[Holiday]:
        """
        Get active holidays (not deleted)

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of active holidays
        """
        return await self.get_all_holidays(skip, limit, include_inactive=False)

    async def get_holidays_by_date_range(
        self,
        start_date: date,
        end_date: date,
        holiday_type: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
        include_inactive: bool = True,
    ) -> List[Holiday]:
        """
        Get holidays within a date range

        Args:
            start_date: Start date of the range
            end_date: End date of the range
            holiday_type: Optional filter by holiday type
            skip: Number of records to skip
            limit: Maximum number of records to return
            include_inactive: Whether to include inactive holidays

        Returns:
            List of holidays in the date range
        """
        if include_inactive:
            query = select(self.model).where(
                and_(
                    self.model.date >= start_date,
                    self.model.date <= end_date,
                    self.model.is_deleted == False,
                )
            )
        else:
            query = select(self.model).where(
                and_(
                    self.model.date >= start_date,
                    self.model.date <= end_date,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )

        if holiday_type:
            query = query.where(self.model.holiday_type == holiday_type)

        query = query.order_by(asc(self.model.date)).offset(skip).limit(limit)

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_holidays_by_type(
        self, holiday_type: str, skip: int = 0, limit: int = 100, include_inactive: bool = True
    ) -> List[Holiday]:
        """
        Get holidays by type

        Args:
            holiday_type: Type of holiday (PREDEFINED or PERSONAL)
            skip: Number of records to skip
            limit: Maximum number of records to return
            include_inactive: Whether to include inactive holidays

        Returns:
            List of holidays of the specified type
        """
        if include_inactive:
            query = (
                select(self.model)
                .where(
                    and_(
                        self.model.holiday_type == holiday_type,
                        self.model.is_deleted == False,
                    )
                )
                .order_by(asc(self.model.date))
                .offset(skip)
                .limit(limit)
            )
        else:
            query = (
                select(self.model)
                .where(
                    and_(
                        self.model.holiday_type == holiday_type,
                        self.model.is_active == True,
                        self.model.is_deleted == False,
                    )
                )
                .order_by(asc(self.model.date))
                .offset(skip)
                .limit(limit)
            )

        result = await self.db.execute(query)
        return result.scalars().all()

    async def search_holidays(
        self, search_term: str, skip: int = 0, limit: int = 100, include_inactive: bool = True
    ) -> List[Holiday]:
        """
        Search holidays by description

        Args:
            search_term: Term to search for in description
            skip: Number of records to skip
            limit: Maximum number of records to return
            include_inactive: Whether to include inactive holidays

        Returns:
            List of holidays matching the search term
        """
        if include_inactive:
            query = (
                select(self.model)
                .where(
                    and_(
                        self.model.description.ilike(f"%{search_term}%"),
                        self.model.is_deleted == False,
                    )
                )
                .order_by(asc(self.model.date))
                .offset(skip)
                .limit(limit)
            )
        else:
            query = (
                select(self.model)
                .where(
                    and_(
                        self.model.description.ilike(f"%{search_term}%"),
                        self.model.is_active == True,
                        self.model.is_deleted == False,
                    )
                )
                .order_by(asc(self.model.date))
                .offset(skip)
                .limit(limit)
            )

        result = await self.db.execute(query)
        return result.scalars().all()

    async def soft_delete(self, holiday_id: UUID) -> Optional[Holiday]:
        """
        Soft delete a holiday by setting is_deleted=True and deleted_at=now()

        Args:
            holiday_id: ID of the holiday to delete

        Returns:
            Updated holiday instance or None if not found
        """
        holiday = await self.get(holiday_id)
        if not holiday:
            return None

        holiday.is_deleted = True
        holiday.deleted_at = datetime.now(timezone.utc)

        await self.db.commit()
        await self.db.refresh(holiday)

        logger.info(f"Holiday soft deleted: {holiday_id}")
        return holiday

    async def count_active_holidays(
        self, filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        Count active holidays with optional filtering

        Args:
            filters: Optional filters to apply

        Returns:
            Number of active holidays
        """
        query = select(func.count(self.model.id)).where(
            and_(self.model.is_active == True, self.model.is_deleted == False)
        )

        # Apply additional filters if provided
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    query = query.where(getattr(self.model, field) == value)

        result = await self.db.execute(query)
        return result.scalar() or 0

    async def get_upcoming_holidays(
        self, days_ahead: int = 30, holiday_type: Optional[str] = None, limit: int = 10
    ) -> List[Holiday]:
        """
        Get upcoming holidays within specified days

        Args:
            days_ahead: Number of days to look ahead
            holiday_type: Optional filter by holiday type
            limit: Maximum number of holidays to return

        Returns:
            List of upcoming holidays
        """
        today = date.today()
        future_date = date.fromordinal(today.toordinal() + days_ahead)

        query = select(self.model).where(
            and_(
                self.model.date >= today,
                self.model.date <= future_date,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )

        if holiday_type:
            query = query.where(self.model.holiday_type == holiday_type)

        query = query.order_by(asc(self.model.date)).limit(limit)

        result = await self.db.execute(query)
        return result.scalars().all()
