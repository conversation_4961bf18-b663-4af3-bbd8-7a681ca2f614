"""
Document Repository
Repository layer for document data access operations
"""

import uuid
from typing import List, Optional, Tuple
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timezone

from app.models.document import Document
from app.repositories.base import BaseRepository
from app.schemas.document import DocumentCreateRequest
from app.core.logging import logger


class DocumentRepository(BaseRepository[Document, DocumentCreateRequest, dict]):
    """Repository for document operations"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Document, db)
    
    async def get_by_id(self, document_id: str) -> Optional[Document]:
        """Get document by ID"""
        try:
            query = select(Document).where(
                and_(
                    Document.id == document_id,
                    Document.is_deleted == False
                )
            )
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting document by ID {document_id}: {e}")
            raise
    
    async def get_all_with_filters(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        user_id: Optional[str] = None,
        franchisor_id: Optional[str] = None,
        file_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        include_deleted: bool = False
    ) -> Tuple[List[Document], int]:
        """Get documents with filtering, search, and pagination"""
        try:
            # Base query
            query = select(Document)
            count_query = select(func.count(Document.id))
            
            # Build filters
            filters = []
            
            if not include_deleted:
                filters.append(Document.is_deleted == False)
            
            if search:
                search_filter = or_(
                    Document.name.ilike(f"%{search}%"),
                    Document.description.ilike(f"%{search}%")
                )
                filters.append(search_filter)
            
            if user_id:
                filters.append(Document.user_id == user_id)
            
            if franchisor_id:
                filters.append(Document.franchisor_id == franchisor_id)
            
            if file_type:
                filters.append(Document.file_type.ilike(f"%{file_type}%"))
            
            if is_active is not None:
                filters.append(Document.is_active == is_active)
            
            # Apply filters
            if filters:
                query = query.where(and_(*filters))
                count_query = count_query.where(and_(*filters))
            
            # Get total count
            count_result = await self.db.execute(count_query)
            total_count = count_result.scalar()
            
            # Apply pagination and ordering
            query = query.order_by(Document.created_at.desc()).offset(skip).limit(limit)
            
            # Execute query
            result = await self.db.execute(query)
            documents = result.scalars().all()
            
            return list(documents), total_count
            
        except Exception as e:
            logger.error(f"Error getting documents with filters: {e}")
            raise
    
    async def create_document(self, document_data: dict) -> Document:
        """Create a new document"""
        try:
            # Ensure UUID is set
            if 'id' not in document_data:
                document_data['id'] = uuid.uuid4()
            
            document = Document(**document_data)
            self.db.add(document)
            await self.db.commit()
            await self.db.refresh(document)
            
            logger.info(f"Created document: {document.id}")
            return document
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating document: {e}")
            raise
    

    
    async def soft_delete_document(self, document_id: str) -> bool:
        """Soft delete document"""
        try:
            update_data = {
                'is_deleted': True,
                'deleted_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc)
            }
            
            query = update(Document).where(
                and_(
                    Document.id == document_id,
                    Document.is_deleted == False
                )
            ).values(**update_data)
            
            result = await self.db.execute(query)
            await self.db.commit()
            
            return result.rowcount > 0
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error soft deleting document {document_id}: {e}")
            raise
    

    
    async def update_status(self, document_id: str, is_active: bool) -> Optional[Document]:
        """Update document active status"""
        try:
            update_data = {
                'is_active': is_active,
                'updated_at': datetime.now(timezone.utc)
            }
            
            query = update(Document).where(
                and_(
                    Document.id == document_id,
                    Document.is_deleted == False
                )
            ).values(**update_data)
            
            await self.db.execute(query)
            await self.db.commit()
            
            return await self.get_by_id(document_id)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating document status {document_id}: {e}")
            raise
    
    async def get_by_franchisor(self, franchisor_id: str, is_active: Optional[bool] = None) -> List[Document]:
        """Get documents by franchisor ID"""
        try:
            filters = [
                Document.franchisor_id == franchisor_id,
                Document.is_deleted == False
            ]
            
            if is_active is not None:
                filters.append(Document.is_active == is_active)
            
            query = select(Document).where(and_(*filters)).order_by(Document.created_at.desc())
            result = await self.db.execute(query)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"Error getting documents by franchisor {franchisor_id}: {e}")
            raise
    
    async def count_by_filters(
        self,
        user_id: Optional[str] = None,
        franchisor_id: Optional[str] = None,
        file_type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> int:
        """Count documents with filters"""
        try:
            filters = [Document.is_deleted == False]
            
            if user_id:
                filters.append(Document.user_id == user_id)
            if franchisor_id:
                filters.append(Document.franchisor_id == franchisor_id)
            if file_type:
                filters.append(Document.file_type.ilike(f"%{file_type}%"))
            if is_active is not None:
                filters.append(Document.is_active == is_active)
            
            query = select(func.count(Document.id)).where(and_(*filters))
            result = await self.db.execute(query)
            return result.scalar()
            
        except Exception as e:
            logger.error(f"Error counting documents: {e}")
            raise
