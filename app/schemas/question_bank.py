"""
Question Bank Schemas
Pydantic models for question bank API requests and responses
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import uuid




class FranchisorDropdownResponse(BaseModel):
    """Response model for franchisor dropdown."""
    id: uuid.UUID = Field(..., description="Franchisor ID", example="550e8400-e29b-41d4-a716-************")
    name: str = Field(..., description="Franchisor name", example="McDonald's")
    category: str = Field(..., description="Franchisor category", example="Food & Beverage")


class QuestionCreateRequest(BaseModel):
    """Request model for creating a new prequalification question."""
    franchisor_id: uuid.UUID = Field(..., description="Franchisor ID", example="550e8400-e29b-41d4-a716-************")
    question_text: str = Field(..., description="Question text", example="What is your investment budget?")
    question_type: str = Field(..., description="Type of question", example="multiple_choice")
    expected_answer: Optional[List[str]] = Field(None, description="Array of expected answers", example=["$50,000-$100,000", "$100,000-$200,000", "$200,000+"])

    # New fields for pre-qualification questions
    question_id: Optional[str] = Field(None, max_length=10, description="Question identifier", example="Q001")
    category: Optional[str] = Field(None, max_length=50, description="Question category", example="financial")
    score_weight: Optional[int] = Field(None, description="Score weight for the question", example=40)
    context_info: Optional[Dict[str, Any]] = Field(None, description="Additional context information as JSON")
    qualification_weight: Optional[float] = Field(None, description="Qualification weight (0.0-1.0)", example=0.4)
    expected_answer_type: Optional[str] = Field(None, max_length=50, description="Expected answer type", example="choice")
    answer_options: Optional[str] = Field(None, description="Available answer options as string")
    passing_criteria: Optional[str] = Field(None, description="Criteria for passing as JSON string")
    validation_rules: Optional[str] = Field(None, description="Validation rules as JSON string")
    requires_follow_up: Optional[bool] = Field(None, description="Whether follow-up is required")
    follow_up_logic: Optional[str] = Field(None, description="Follow-up logic description")
    is_required: Optional[bool] = Field(None, description="Whether the question is required")


class QuestionUpdateRequest(BaseModel):
    """Request model for updating a prequalification question."""
    question_text: str = Field(..., description="Question text", example="What is your investment budget?")
    question_type: str = Field(..., description="Type of question", example="multiple_choice")
    expected_answer: Optional[List[str]] = Field(None, description="Array of expected answers", example=["$50,000-$100,000", "$100,000-$200,000", "$200,000+"])

    # New fields for pre-qualification questions
    question_id: Optional[str] = Field(None, max_length=10, description="Question identifier", example="Q001")
    category: Optional[str] = Field(None, max_length=50, description="Question category", example="financial")
    score_weight: Optional[int] = Field(None, description="Score weight for the question", example=40)
    context_info: Optional[Dict[str, Any]] = Field(None, description="Additional context information as JSON")
    qualification_weight: Optional[float] = Field(None, description="Qualification weight (0.0-1.0)", example=0.4)
    expected_answer_type: Optional[str] = Field(None, max_length=50, description="Expected answer type", example="choice")
    answer_options: Optional[str] = Field(None, description="Available answer options as string")
    passing_criteria: Optional[str] = Field(None, description="Criteria for passing as JSON string")
    validation_rules: Optional[str] = Field(None, description="Validation rules as JSON string")
    requires_follow_up: Optional[bool] = Field(None, description="Whether follow-up is required")
    follow_up_logic: Optional[str] = Field(None, description="Follow-up logic description")
    is_required: Optional[bool] = Field(None, description="Whether the question is required")
  

class QuestionResponse(BaseModel):
    """Response model for prequalification question details."""
    id: uuid.UUID = Field(..., description="Question ID", example="550e8400-e29b-41d4-a716-************")
    franchisor_id: uuid.UUID = Field(..., description="Franchisor ID", example="550e8400-e29b-41d4-a716-************")
    franchisor_name: str = Field(..., description="Franchisor name", example="McDonald's")
    franchisor_category: str = Field(..., description="Franchisor category", example="Food & Beverage")
    question_text: str = Field(..., description="Question text", example="What is your investment budget?")
    question_internal_text: str = Field(..., description="Original question text (immutable)", example="What is your investment budget?")
    question_type: str = Field(..., description="Type of question", example="multiple_choice")
    expected_answer: Optional[List[str]] = Field(None, description="Array of expected answers", example=["$50,000-$100,000", "$100,000-$200,000", "$200,000+"])
    order_sequence: int = Field(..., description="Question order", example=1)
    is_active: bool = Field(..., description="Question active status", example=True)
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    # New fields for pre-qualification questions
    question_id: Optional[str] = Field(None, description="Question identifier", example="Q001")
    category: Optional[str] = Field(None, description="Question category", example="financial")
    score_weight: Optional[int] = Field(None, description="Score weight for the question", example=40)
    context_info: Optional[Dict[str, Any]] = Field(None, description="Additional context information as JSON")
    qualification_weight: Optional[float] = Field(None, description="Qualification weight (0.0-1.0)", example=0.4)
    expected_answer_type: Optional[str] = Field(None, description="Expected answer type", example="choice")
    answer_options: Optional[str] = Field(None, description="Available answer options as string")
    passing_criteria: Optional[str] = Field(None, description="Criteria for passing as JSON string")
    validation_rules: Optional[str] = Field(None, description="Validation rules as JSON string")
    requires_follow_up: Optional[bool] = Field(None, description="Whether follow-up is required")
    follow_up_logic: Optional[str] = Field(None, description="Follow-up logic description")
    is_required: Optional[bool] = Field(None, description="Whether the question is required")
    is_deleted: bool = Field(..., description="Whether the question is deleted")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")


class QuestionListResponse(BaseModel):
    """Response model for question list."""
    id: uuid.UUID = Field(..., description="Question ID", example="550e8400-e29b-41d4-a716-************")
    franchisor_id: uuid.UUID = Field(..., description="Franchisor ID", example="550e8400-e29b-41d4-a716-************")
    franchisor_name: str = Field(..., description="Franchisor name", example="McDonald's")
    franchisor_category: str = Field(..., description="Franchisor category", example="Food & Beverage")
    question_text: str = Field(..., description="Question text", example="What is your investment budget?")
    question_type: str = Field(..., description="Type of question", example="multiple_choice")
    expected_answer: Optional[List[str]] = Field(None, description="Array of expected answers", example=["$50,000-$100,000", "$100,000-$200,000", "$200,000+"])
    order_sequence: int = Field(..., description="Question order", example=1)
    is_active: bool = Field(..., description="Question active status", example=True)
    created_at: datetime = Field(..., description="Creation timestamp")

    # New fields for pre-qualification questions
    question_id: Optional[str] = Field(None, description="Question identifier", example="Q001")
    category: Optional[str] = Field(None, description="Question category", example="financial")
    score_weight: Optional[int] = Field(None, description="Score weight for the question", example=40)
    context_info: Optional[Dict[str, Any]] = Field(None, description="Additional context information as JSON")
    qualification_weight: Optional[float] = Field(None, description="Qualification weight (0.0-1.0)", example=0.4)
    expected_answer_type: Optional[str] = Field(None, description="Expected answer type", example="choice")
    answer_options: Optional[str] = Field(None, description="Available answer options as string")
    passing_criteria: Optional[str] = Field(None, description="Criteria for passing as JSON string")
    validation_rules: Optional[str] = Field(None, description="Validation rules as JSON string")
    requires_follow_up: Optional[bool] = Field(None, description="Whether follow-up is required")
    follow_up_logic: Optional[str] = Field(None, description="Follow-up logic description")
    is_required: Optional[bool] = Field(None, description="Whether the question is required")
    is_deleted: bool = Field(..., description="Whether the question is deleted")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")


class QuestionReorderItem(BaseModel):
    """Model for reordering question items."""
    question_id: uuid.UUID = Field(..., description="Question ID", example="550e8400-e29b-41d4-a716-************")
    order_sequence: int = Field(..., description="New order position", example=1)


class QuestionReorderRequest(BaseModel):
    """Request model for reordering questions."""
    franchisor_id: uuid.UUID = Field(..., description="Franchisor ID", example="550e8400-e29b-41d4-a716-************")
    questions: List[QuestionReorderItem] = Field(..., description="List of questions with new orders")


class QuestionToggleRequest(BaseModel):
    """Request model for toggling question status."""
    question_id: uuid.UUID = Field(..., description="Question ID to toggle", example="550e8400-e29b-41d4-a716-************")


class QuestionDeleteRequest(BaseModel):
    """Request model for deleting a question."""
    question_id: uuid.UUID = Field(..., description="Question ID to delete", example="550e8400-e29b-41d4-a716-************")


class QuestionListPaginatedResponse(BaseModel):
    """Paginated response model for question list."""
    items: List[QuestionListResponse] = Field(..., description="List of questions")
    total: int = Field(..., description="Total number of questions", example=100)
    page: int = Field(..., description="Current page number", example=1)
    size: int = Field(..., description="Page size", example=10)
    pages: int = Field(..., description="Total number of pages", example=10) 