"""Document schemas for request/response validation"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class DocumentType(str, Enum):
    """Document type"""
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    IMAGE = "image"
    OTHER = "other"


class DocumentStatus(str, Enum):
    """Document processing status"""
    PENDING = "pending"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"


class DocumentBase(BaseModel):
    """Base document schema"""
    name: str = Field(..., description="Document name", max_length=255)
    description: Optional[str] = Field(None, description="Document description")
    file_type: Optional[str] = Field(None, description="File type")
    file_size: Optional[str] = Field(None, description="File size")


class DocumentCreateRequest(DocumentBase):
    """Document creation request"""
    file_path: str = Field(..., description="File path")
    user_id: Optional[str] = Field(None, description="Associated user ID")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    is_active: bool = Field(True, description="Whether the document is active")





class DocumentResponse(DocumentBase):
    """Document response"""
    id: str = Field(..., description="Document ID")
    file_path: str = Field(..., description="File path")
    file_url: Optional[str] = Field(None, description="Full S3 URL for the document")
    user_id: Optional[str] = Field(None, description="Associated user ID")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    is_active: bool = Field(..., description="Whether the document is active")
    is_deleted: bool = Field(..., description="Whether the document is deleted")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")

    class Config:
        from_attributes = True


class DocumentListResponse(BaseModel):
    """Document list response"""
    items: list[DocumentResponse] = Field(..., description="List of documents")
    total_count: int = Field(..., description="Total number of documents")
    pagination: dict = Field(..., description="Pagination information")


class DocumentSearchRequest(BaseModel):
    """Document search request"""
    search: Optional[str] = Field(None, description="Search term")
    user_id: Optional[str] = Field(None, description="Filter by user ID")
    franchisor_id: Optional[str] = Field(None, description="Filter by franchisor ID")
    file_type: Optional[str] = Field(None, description="Filter by file type")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    page: int = Field(1, description="Page number", ge=1)
    limit: int = Field(20, description="Items per page", ge=1, le=100)


class DocumentUploadRequest(BaseModel):
    """Schema for document upload request"""
    name: str = Field(..., description="Document name")
    description: Optional[str] = Field(None, description="Document description")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    is_active: bool = Field(True, description="Whether the document is active")


class DocumentBulkUploadRequest(BaseModel):
    """Schema for bulk document upload request"""
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    is_active: bool = Field(True, description="Whether the documents are active")





class DocumentStatusUpdateRequest(BaseModel):
    """Schema for document status update request"""
    is_active: bool = Field(..., description="Whether the document is active")


class DocumentUploadResponse(BaseModel):
    """Schema for document upload response"""
    document_id: str = Field(..., description="Document ID")
    upload_url: Optional[str] = Field(None, description="Pre-signed upload URL")
    status: str = Field(..., description="Upload status")
    message: str = Field(..., description="Upload status message")


class DocumentSearchResult(BaseModel):
    """Schema for document search result"""
    document_id: str = Field(..., description="Document ID")
    title: str = Field(..., description="Document title")
    description: Optional[str] = Field(None, description="Document description")
    relevance_score: float = Field(..., description="Search relevance score")
    matched_content: Optional[str] = Field(None, description="Matched content snippet")
    document_type: DocumentType = Field(..., description="Document type")
    tags: Optional[List[str]] = Field(None, description="Document tags")


class DocumentSearchResponse(BaseModel):
    """Schema for document search response"""
    query: str = Field(..., description="Search query")
    results: List[DocumentSearchResult] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results")
    search_time: float = Field(..., description="Search execution time in seconds")


class DocumentProcessingStatus(BaseModel):
    """Schema for document processing status"""
    document_id: str = Field(..., description="Document ID")
    status: DocumentStatus = Field(..., description="Processing status")
    progress: Optional[int] = Field(None, ge=0, le=100, description="Processing progress percentage")
    message: Optional[str] = Field(None, description="Processing status message")
    error_details: Optional[str] = Field(None, description="Error details if processing failed")
    started_at: Optional[datetime] = Field(None, description="Processing start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Processing completion timestamp")


class DocumentAnalysisRequest(BaseModel):
    """Schema for document analysis request"""
    document_id: str = Field(..., description="Document ID")
    analysis_type: str = Field(..., description="Type of analysis to perform")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Analysis parameters")


class DocumentAnalysisResponse(BaseModel):
    """Schema for document analysis response"""
    document_id: str = Field(..., description="Document ID")
    analysis_type: str = Field(..., description="Type of analysis performed")
    results: Dict[str, Any] = Field(..., description="Analysis results")
    confidence_score: Optional[float] = Field(None, description="Analysis confidence score")
    analysis_date: datetime = Field(..., description="Analysis timestamp")


class DocumentSummaryRequest(BaseModel):
    """Schema for document summary request"""
    document_id: str = Field(..., description="Document ID")
    summary_length: Optional[str] = Field("medium", description="Summary length (short, medium, long)")
    focus_areas: Optional[List[str]] = Field(None, description="Specific areas to focus on in summary")


class DocumentSummaryResponse(BaseModel):
    """Schema for document summary response"""
    document_id: str = Field(..., description="Document ID")
    summary: str = Field(..., description="Document summary")
    key_points: List[str] = Field(..., description="Key points from the document")
    word_count: int = Field(..., description="Summary word count")
    original_word_count: int = Field(..., description="Original document word count")
    compression_ratio: float = Field(..., description="Summary compression ratio")


class DocumentMetadata(BaseModel):
    """Schema for document metadata"""
    document_id: str = Field(..., description="Document ID")
    file_name: str = Field(..., description="Original file name")
    file_extension: str = Field(..., description="File extension")
    mime_type: str = Field(..., description="MIME type")
    file_size: int = Field(..., description="File size in bytes")
    page_count: Optional[int] = Field(None, description="Number of pages (for documents)")
    word_count: Optional[int] = Field(None, description="Word count")
    character_count: Optional[int] = Field(None, description="Character count")
    language: Optional[str] = Field(None, description="Detected language")
    encoding: Optional[str] = Field(None, description="File encoding")
    checksum: Optional[str] = Field(None, description="File checksum")
    extracted_text_length: Optional[int] = Field(None, description="Length of extracted text")
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
