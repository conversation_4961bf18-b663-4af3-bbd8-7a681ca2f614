networks:
  growthhive_network:
    driver: bridge
 
services:
 
  backend:
    container_name: growthhive-backend
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env
    environment:
      - TZ=Asia/Kolkata
      - RABBITMQ_HOST=rabbitmq
      - REDIS_HOST=redis
    ports:
      - "8000:8000"
    volumes:
      - .:/growthhive
    depends_on:
      - rabbitmq
      - redis
    networks:
      - growthhive_network
    restart: always
 
  rabbitmq:
    container_name: rabbitmq
    image: rabbitmq:3.12-management
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - growthhive_network
    restart: always
 
  redis:
    container_name: redis
    image: redis:6.2
    ports:
      - "6379:6379"
    networks:
      - growthhive_network
    restart: always
