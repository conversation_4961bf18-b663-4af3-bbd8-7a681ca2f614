#!/usr/bin/env python3
"""
Simple database connection test
"""

import asyncio
from app.core.database.connection import async_engine
from app.core.logging import logger

async def test_connection():
    """Test database connection"""
    try:
        logger.info("🔍 Testing database connection...")
        
        if not async_engine:
            logger.error("❌ Database engine not initialized")
            return False
        
        # Test connection
        async with async_engine.begin() as conn:
            from sqlalchemy import text
            result = await conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            logger.info(f"✅ Database connection successful: {row}")
            return True
            
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    if success:
        print("✅ Database connection test passed")
    else:
        print("❌ Database connection test failed")
